# 统一支付网关设计文档

## 概述

本文档描述了支付网关的统一支付模型设计，采用**统一模型 + 适配器模式**的架构，为不同的支付渠道（DinPay、微信、支付宝等）提供标准化的接口。

## 核心设计理念

### 1. 统一模型
- 参考微信支付、支付宝等主流支付平台的标准字段设计
- 所有接口的入参和出参都使用标准化的数据传输对象（DTO）
- 统一的状态枚举、错误码和异常体系
- 渠道无关的配置抽象

### 2. 适配器模式
- 每个支付渠道都有专属的适配器实现
- 适配器专注于特性发现和字段转换
- 支持特性发现机制，优雅处理渠道功能差异

## 包结构设计

```
net.summerfarm.payment.gateway
├── common/                 # 公共组件
│   ├── enums/             # 枚举定义
│   └── exception/         # 异常体系
├── model/                 # 数据模型
├── adapter/               # 渠道适配器
│   └── impl/             # 适配器实现
└── service/               # 服务接口
    └── impl/             # 服务实现
```

## 核心组件

### 1. 统一请求模型 (UnifiedPaymentRequest)

参考微信支付官方API设计，包含标准字段：

```java
public class UnifiedPaymentRequest {
    // 基础订单信息
    private String paymentNo;        // 商户订单号
    private String description;       // 商品描述
    private String attach;            // 商户数据包
    
    // 金额信息
    private Integer totalAmount;      // 订单总金额（分）
    private String currency;          // 货币类型
    
    // 时间信息
    private LocalDateTime timeExpire; // 交易结束时间
    
    // 通知信息
    private String notifyUrl;         // 通知地址
    private String returnUrl;         // 返回地址
    
    // 商品详情
    private List<GoodsDetail> goodsDetail;
    
    // 场景信息
    private SceneInfo sceneInfo;
    
    // 支付者信息
    private PayerInfo payer;
    
    // 结算信息
    private SettleInfo settleInfo;
    
    // 业务扩展字段
    private Long tenantId;            // 租户ID
    private String businessLine;      // 业务线标识
    private String paymentMethod;     // 支付方式
    private String platform;         // 应用平台
    private ChannelConfig channelConfig; // 渠道配置
    // ... 其他字段
}
```

### 2. 统一响应模型 (UnifiedPaymentResult)

```java
public class UnifiedPaymentResult {
    private PaymentStatus status;           // 统一状态
    private String paymentNo;              // 商户订单号
    private String paymentOrderId;          // SDK内部支付单ID
    private String channelTransactionId;    // 渠道交易流水号
    private PaymentCredential credential;   // 支付凭证（二维码等）
    private String rawResponse;             // 渠道原始报文
    private UnifiedError error;             // 错误信息
    // ... 其他字段
}
```

### 3. 支持的数据模型

- **GoodsDetail**: 商品详情信息
- **SceneInfo**: 场景信息（包含门店信息、H5信息等）
- **PayerInfo**: 支付者信息
- **SettleInfo**: 结算信息（包含分账标识）
- **ChannelConfig**: 通用渠道配置

### 4. 统一状态枚举 (PaymentStatus)

- `SUCCESS`: 支付成功
- `FAILED`: 支付失败
- `PENDING`: 支付处理中
- `CLOSED`: 支付已关闭
- `CANCELLED`: 支付已取消
- `REFUNDED`: 支付已退款
- `PARTIAL_REFUNDED`: 部分退款

### 5. 特性发现机制 (FeatureType)

```java
public enum FeatureType {
    REAL_TIME_PROFIT_SHARING,    // 实时分账
    DELAY_PROFIT_SHARING,        // 延迟分账
    ALLOW_ONE_CENT_ORDER,        // 一分钱订单
    REFUND_SUPPORT,              // 支持退款
    H5_PAYMENT_SUPPORT,          // 支持H5支付
    MINI_APP_PAYMENT_SUPPORT,    // 支持小程序支付
    // ... 其他特性
}
```

## 服务接口

### 1. 支付客户端服务 (PaymentClientService)

```java
public interface PaymentClientService {
    UnifiedPaymentResult pay(UnifiedPaymentRequest request);
    // TODO: 后续扩展
    // UnifiedRefundResult refund(UnifiedRefundRequest request);
    // UnifiedQueryResult queryPayment(UnifiedQueryRequest request);
}
```

### 2. 渠道适配器接口 (ChannelAdapter)

```java
public interface ChannelAdapter {
    String getChannelCode();
    String getChannelName();
    Set<FeatureType> getSupportedFeatures();
    UnifiedPaymentResult doPay(UnifiedPaymentRequest request);
    // 特性发现和字段转换
}
```

## DinPay适配器实现

DinPay适配器支持的特性：
- H5支付、小程序支付、扫码支付
- 退款、部分退款
- 订单查询、关闭订单
- 异步通知
- 一分钱订单
- **不支持分账功能**

## 使用示例

### DinPay微信小程序支付

```java
// 1. 构建渠道配置
ChannelConfig channelConfig = ChannelConfig.builder()
    .channelCode("dinpay")
    .merchantNo("your_merchant_no")
    .privateKey("your_private_key")
    .publicKey("your_public_key")
    .build();

// 2. 构建支付请求
UnifiedPaymentRequest request = UnifiedPaymentRequest.builder()
    .paymentNo("ORDER_" + System.currentTimeMillis())
    .description("测试商品")
    .totalAmount(9999) // 99.99元，单位分
    .currency("CNY")
    .notifyUrl("https://your-domain.com/payment/notify")
    .payer(PayerInfo.builder().openid("user_openid_123").build())
    .sceneInfo(SceneInfo.builder().payerClientIp("*************").build())
    .tenantId(1001L)
    .businessLine("saas")
    .paymentMethod("wechat")
    .platform("miniapp")
    .channelConfig(channelConfig)
    .build();

// 3. 发起支付
UnifiedPaymentResult result = paymentClientService.pay(request);

// 4. 处理结果
if (result.isSuccess()) {
    System.out.println("支付成功");
} else if (result.isPending()) {
    System.out.println("二维码: " + result.getCredential().getContent());
} else {
    System.out.println("支付失败: " + result.getError().getMessage());
}
```

## 单元测试

项目包含完整的单元测试：

- **PaymentClientServiceTest**: 支付服务测试
- **DinPayChannelAdapterTest**: DinPay适配器测试
- **UnifiedPaymentRequestTest**: 请求模型测试

运行测试：
```bash
mvn test
```

## 架构优势

1. **标准化设计**: 参考主流支付平台的标准字段设计
2. **统一接口**: 业务方只需要学习一套API
3. **易于扩展**: 新增支付渠道只需实现ChannelAdapter接口
4. **特性发现**: 自动检查渠道功能支持，避免无效调用
5. **错误处理**: 统一的异常体系，便于错误处理和监控
6. **可测试性**: 完整的单元测试覆盖
7. **多租户支持**: 原生支持SaaS场景的多租户需求

## 金额处理

- **统一单位**: 所有金额统一使用分为单位（Integer类型）
- **转换工具**: 提供元分转换的工具方法
- **精度保证**: 避免浮点数精度问题

## 后续扩展计划

1. 实现退款、查询、关单等其他接口
2. 添加更多渠道适配器（微信原生、支付宝等）
3. 实现回调处理服务 (CallbackService)
4. 添加分账功能支持
5. 完善监控和日志体系

## 注意事项

1. 当前设计保持了与现有鲜沐商城业务的兼容性
2. SaaS租户通过方法参数传递租户ID
3. 分账功能暂时预留，后续实现
4. 不包含traceId字段，由应用方自行管理
5. 包名使用gateway而非sdk，更准确地表达功能职责
