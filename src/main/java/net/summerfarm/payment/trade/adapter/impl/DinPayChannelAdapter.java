package net.summerfarm.payment.trade.adapter.impl;

import net.summerfarm.payment.trade.adapter.ChannelAdapter;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.enums.FeatureType;
import net.summerfarm.payment.trade.common.enums.PaymentStatus;
import net.summerfarm.payment.trade.common.exception.ChannelException;
import net.summerfarm.payment.trade.model.channel.DinPayPaymentRequest;
import net.summerfarm.payment.trade.model.channel.DinPayPaymentResponse;
import net.summerfarm.payment.trade.model.common.PaymentCredential;
import net.summerfarm.payment.trade.model.config.ChannelConfig;
import net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.response.UnifiedPaymentResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.EnumSet;
import java.util.Set;

/**
 * DinPay（智付）渠道适配器实现
 * 专注于参数转换和特性发现
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Component
public class DinPayChannelAdapter implements ChannelAdapter<DinPayPaymentRequest, DinPayPaymentResponse> {

    private static final Logger log = LoggerFactory.getLogger(DinPayChannelAdapter.class);

    private static final String CHANNEL_CODE = "dinpay";
    private static final String CHANNEL_NAME = "DinPay智付";

    @Override
    public String getChannelCode() {
        return CHANNEL_CODE;
    }

    @Override
    public String getChannelName() {
        return CHANNEL_NAME;
    }

    @Override
    public Set<FeatureType> getSupportedFeatures() {
        // TODO: 根据实际DinPay接口文档确定支持的特性
        return EnumSet.of(
                FeatureType.H5_PAYMENT_SUPPORT,
                FeatureType.MINI_APP_PAYMENT_SUPPORT,
                FeatureType.QR_CODE_PAYMENT_SUPPORT,
                FeatureType.REFUND_SUPPORT,
                FeatureType.PARTIAL_REFUND_SUPPORT,
                FeatureType.ORDER_QUERY_SUPPORT,
                FeatureType.ORDER_CLOSE_SUPPORT,
                FeatureType.ASYNC_NOTIFY_SUPPORT,
                FeatureType.ALLOW_ONE_CENT_ORDER
                // 注意：DinPay是否支持分账功能需要根据实际接口文档确定
        );
    }

    @Override
    public DinPayPaymentRequest convertPaymentRequest(UnifiedPaymentRequest request) {
        // 1. 验证请求参数（最开始就验证）
        validateRequest(request);
        validateDinPaySpecificParams(request);

        log.info("DinPay适配器开始转换支付请求: paymentNo={}, amount={}",
                request.getPaymentNo(), request.getTotalAmount());

        try {
            // 2. 构建DinPay支付请求对象
            DinPayPaymentRequest dinPayRequest = buildDinPayRequest(request);

            log.info("DinPay支付请求转换完成: merchantNo={}, orderNo={}, amount={}",
                    dinPayRequest.getMerchantNo(), dinPayRequest.getOrderNo(), dinPayRequest.getAmount());

            return dinPayRequest;

        } catch (Exception e) {
            log.error("DinPay支付请求转换失败: paymentNo={}", request.getPaymentNo(), e);
            throw new ChannelException(ErrorCode.INVALID_PARAMETER, CHANNEL_NAME,
                    "请求参数转换失败: " + e.getMessage());
        }
    }

    @Override
    public UnifiedPaymentResult convertPaymentResponse(DinPayPaymentResponse channelResponse, UnifiedPaymentRequest originalRequest) {
        // 1. 验证参数（最开始就验证）
        if (channelResponse == null) {
            throw new ChannelException(ErrorCode.SYSTEM_ERROR, CHANNEL_NAME, "渠道响应不能为空");
        }

        if (originalRequest == null) {
            throw new ChannelException(ErrorCode.SYSTEM_ERROR, CHANNEL_NAME, "原始请求不能为空");
        }

        log.info("DinPay适配器开始转换支付响应: paymentNo={}", originalRequest.getPaymentNo());

        try {
            return convertToUnifiedResult(originalRequest, channelResponse);

        } catch (Exception e) {
            log.error("DinPay支付响应转换失败: paymentNo={}", originalRequest.getPaymentNo(), e);
            throw new ChannelException(ErrorCode.SYSTEM_ERROR, CHANNEL_NAME,
                    "响应转换失败: " + e.getMessage());
        }
    }

    /**
     * 验证DinPay特定参数
     */
    private void validateDinPaySpecificParams(UnifiedPaymentRequest request) {
        ChannelConfig config = request.getChannelConfig();

        if (config.getMerchantNo() == null || config.getMerchantNo().trim().isEmpty()) {
            throw new ChannelException(ErrorCode.CHANNEL_CONFIG_ERROR, CHANNEL_NAME,
                    "DinPay商户号不能为空");
        }

        if (config.getPrivateKey() == null || config.getPrivateKey().trim().isEmpty()) {
            throw new ChannelException(ErrorCode.CHANNEL_CONFIG_ERROR, CHANNEL_NAME,
                    "DinPay私钥不能为空");
        }

        if (config.getPublicKey() == null || config.getPublicKey().trim().isEmpty()) {
            throw new ChannelException(ErrorCode.CHANNEL_CONFIG_ERROR, CHANNEL_NAME,
                    "DinPay公钥不能为空");
        }

        // 验证支付者信息
        if (request.getPayer() == null || request.getPayer().getUserId() == null) {
            throw new ChannelException(ErrorCode.CHANNEL_CONFIG_ERROR, CHANNEL_NAME,
                    "支付者userId不能为空");
        }
    }

    /**
     * 构建DinPay支付请求
     */
    private DinPayPaymentRequest buildDinPayRequest(UnifiedPaymentRequest request) {
        ChannelConfig config = request.getChannelConfig();

        return DinPayPaymentRequest.builder()
                .merchantNo(config.getMerchantNo())
                .orderNo(request.getPaymentNo())
                .amount(request.getTotalAmount()) // 已经是分为单位
                .subject(request.getDescription())
                .attach(request.getAttach())
                .notifyUrl(request.getNotifyUrl())
                .returnUrl(null) // TODO: 根据实际需求添加returnUrl字段
                .paymentMethod(mapPaymentMethod(request.getPaymentMethod(), request.getPlatform()))
                .openid(request.getPayer().getUserId())
                .clientIp(null) // TODO: 根据实际需求添加userIp字段
                .build();
    }

    /**
     * 映射支付方式
     */
    private String mapPaymentMethod(String paymentMethod, String platform) {
        // TODO: 根据实际DinPay接口文档确定支付方式映射
        if ("wechat".equals(paymentMethod)) {
            if ("miniapp".equals(platform)) {
                return "WECHAT_MP";
            } else if ("h5".equals(platform)) {
                return "WECHAT_H5";
            }
        } else if ("alipay".equals(paymentMethod)) {
            return "ALIPAY";
        }

        throw new ChannelException(ErrorCode.CHANNEL_FEATURE_NOT_SUPPORTED, CHANNEL_NAME,
                "不支持的支付方式: " + paymentMethod + "@" + platform);
    }

    /**
     * 转换为统一结果
     */
    private UnifiedPaymentResult convertToUnifiedResult(UnifiedPaymentRequest request,
                                                       DinPayPaymentResponse response) {
        if ("SUCCESS".equals(response.getCode())) {
            // 构建支付凭证
            PaymentCredential credential = PaymentCredential.builder()
                    .type("QR_CODE")
                    .content(response.getQrCode())
                    .build();

            return UnifiedPaymentResult.builder()
                    .paymentNo(request.getPaymentNo())
                    .status(PaymentStatus.PENDING)
                    .paymentOrderId(response.getDinpayOrderNo())
                    .credential(credential)
                    .build();
        } else {
            throw new ChannelException(ErrorCode.CHANNEL_UNAVAILABLE, CHANNEL_NAME,
                    response.getCode(), response.getMessage());
        }
    }


}
