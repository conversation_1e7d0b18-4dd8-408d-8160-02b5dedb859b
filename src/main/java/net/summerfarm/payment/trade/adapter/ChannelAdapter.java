package net.summerfarm.payment.trade.adapter;

import net.summerfarm.payment.trade.common.enums.FeatureType;
import net.summerfarm.payment.trade.model.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.UnifiedPaymentResult;

import java.util.Set;

/**
 * 支付渠道适配器接口
 * 专注于参数转换和特性发现，不负责具体的API调用
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
public interface ChannelAdapter {

    /**
     * 获取渠道标识符
     *
     * @return 渠道标识符
     */
    String getChannelCode();

    /**
     * 获取渠道名称
     *
     * @return 渠道名称
     */
    String getChannelName();

    /**
     * 声明该渠道支持的特性
     *
     * @return 支持的特性集合
     */
    Set<FeatureType> getSupportedFeatures();

    /**
     * 转换统一支付请求为渠道特定请求
     *
     * @param request 统一支付请求
     * @return 渠道特定请求对象
     */
    Object convertPaymentRequest(UnifiedPaymentRequest request);

    /**
     * 转换渠道响应为统一支付结果
     *
     * @param channelResponse 渠道原始响应
     * @param originalRequest 原始请求（用于构建响应时的上下文信息）
     * @return 统一支付结果
     */
    UnifiedPaymentResult convertPaymentResponse(Object channelResponse, UnifiedPaymentRequest originalRequest);

    /**
     * 检查是否支持指定特性
     *
     * @param feature 特性类型
     * @return 是否支持
     */
    default boolean supportsFeature(FeatureType feature) {
        Set<FeatureType> supportedFeatures = getSupportedFeatures();
        return supportedFeatures != null && supportedFeatures.contains(feature);
    }

    /**
     * 验证请求参数
     *
     * @param request 支付请求
     * @throws IllegalArgumentException 参数验证失败时抛出
     */
    default void validateRequest(UnifiedPaymentRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("支付请求不能为空");
        }
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new IllegalArgumentException("商户订单号不能为空");
        }
        if (request.getTotalAmount() == null || request.getTotalAmount() <= 0) {
            throw new IllegalArgumentException("支付金额必须大于0");
        }
        if (request.getDescription() == null || request.getDescription().trim().isEmpty()) {
            throw new IllegalArgumentException("商品描述不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new IllegalArgumentException("渠道配置不能为空");
        }
    }

    /**
     * 转换统一退款请求为渠道特定请求
     *
     * @param request 统一退款请求
     * @return 渠道特定退款请求对象
     */
    default Object convertRefundRequest(Object request) {
        throw new UnsupportedOperationException("该渠道暂不支持退款功能");
    }

    /**
     * 转换渠道退款响应为统一退款结果
     *
     * @param channelResponse 渠道退款响应
     * @param originalRequest 原始退款请求
     * @return 统一退款结果
     */
    default Object convertRefundResponse(Object channelResponse, Object originalRequest) {
        throw new UnsupportedOperationException("该渠道暂不支持退款功能");
    }

    /**
     * 转换统一查询请求为渠道特定请求
     *
     * @param request 统一查询请求
     * @return 渠道特定查询请求对象
     */
    default Object convertQueryRequest(Object request) {
        throw new UnsupportedOperationException("该渠道暂不支持查询功能");
    }

    /**
     * 转换渠道查询响应为统一查询结果
     *
     * @param channelResponse 渠道查询响应
     * @param originalRequest 原始查询请求
     * @return 统一查询结果
     */
    default Object convertQueryResponse(Object channelResponse, Object originalRequest) {
        throw new UnsupportedOperationException("该渠道暂不支持查询功能");
    }
}
