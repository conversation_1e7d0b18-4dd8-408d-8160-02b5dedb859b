package net.summerfarm.payment.gateway.adapter;

import net.summerfarm.payment.gateway.common.enums.FeatureType;
import net.summerfarm.payment.gateway.model.UnifiedPaymentRequest;
import net.summerfarm.payment.gateway.model.UnifiedPaymentResult;

import java.util.Set;

/**
 * 支付渠道适配器接口
 * 负责将统一模型转换为渠道特定协议
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
public interface ChannelAdapter {

    /**
     * 获取渠道标识符
     *
     * @return 渠道标识符
     */
    String getChannelCode();

    /**
     * 获取渠道名称
     *
     * @return 渠道名称
     */
    String getChannelName();

    /**
     * 声明该渠道支持的特性
     *
     * @return 支持的特性集合
     */
    Set<FeatureType> getSupportedFeatures();

    /**
     * 执行支付
     *
     * @param request 统一支付请求
     * @return 统一支付结果
     */
    UnifiedPaymentResult doPay(UnifiedPaymentRequest request);

    /**
     * 检查是否支持指定特性
     *
     * @param feature 特性类型
     * @return 是否支持
     */
    default boolean supportsFeature(FeatureType feature) {
        Set<FeatureType> supportedFeatures = getSupportedFeatures();
        return supportedFeatures != null && supportedFeatures.contains(feature);
    }

    /**
     * 验证请求参数
     *
     * @param request 支付请求
     * @throws IllegalArgumentException 参数验证失败时抛出
     */
    default void validateRequest(UnifiedPaymentRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("支付请求不能为空");
        }
        if (request.getOutTradeNo() == null || request.getOutTradeNo().trim().isEmpty()) {
            throw new IllegalArgumentException("商户订单号不能为空");
        }
        if (request.getTotalAmount() == null || request.getTotalAmount() <= 0) {
            throw new IllegalArgumentException("支付金额必须大于0");
        }
        if (request.getDescription() == null || request.getDescription().trim().isEmpty()) {
            throw new IllegalArgumentException("商品描述不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new IllegalArgumentException("渠道配置不能为空");
        }
    }

    // TODO: 后续扩展其他方法
    // UnifiedRefundResult doRefund(UnifiedRefundRequest request);
    // UnifiedQueryResult doQuery(UnifiedQueryRequest request);
    // UnifiedCloseResult doClose(UnifiedCloseRequest request);
}
