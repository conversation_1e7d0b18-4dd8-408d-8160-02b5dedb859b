package net.summerfarm.payment.trade.example;

import net.summerfarm.payment.trade.model.common.PayerInfo;
import net.summerfarm.payment.trade.model.config.ChannelConfig;
import net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.response.UnifiedPaymentResult;
import net.summerfarm.payment.trade.service.PaymentClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 新架构使用示例
 * 展示适配器 + 客户端分离的架构优势
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Component
public class NewArchitectureExample {

    @Autowired
    private PaymentClientService paymentClientService;

    /**
     * DinPay支付示例
     */
    public void dinPayPaymentExample() {
        System.out.println("=== DinPay支付示例（新架构）===");

        // 1. 构建渠道配置
        ChannelConfig channelConfig = ChannelConfig.builder()
                .channelCode("dinpay")
                .channelName("DinPay智付")
                .merchantNo("your_merchant_no")
                .privateKey("your_private_key")
                .publicKey("your_public_key")
                .secretKey("your_secret_key")
                .build();

        // 2. 构建支付者信息
        PayerInfo payer = PayerInfo.builder()
                .userId("user_123456")
                .build();

        // 3. 构建支付请求
        UnifiedPaymentRequest request = UnifiedPaymentRequest.builder()
                .paymentNo("ORDER_" + System.currentTimeMillis())
                .description("测试订单")
                .attach("test_attach_data")
                .totalAmount(9999) // 99.99元，单位分
                .currency("CNY")
                .notifyUrl("https://test.example.com/notify")
                .payer(payer)
                .tenantId(1001L)
                .businessLine("saas")
                .paymentMethod("wechat")
                .platform("miniapp")
                .channelConfig(channelConfig)
                .build();

        // 4. 发起支付（内部会自动使用适配器和客户端）
        UnifiedPaymentResult result = paymentClientService.pay(request);

        // 5. 处理结果
        handlePaymentResult(result);
    }

    /**
     * 处理支付结果
     */
    private void handlePaymentResult(UnifiedPaymentResult result) {
        System.out.println("=== 支付结果 ===");
        System.out.println("订单号: " + result.getPaymentNo());
        System.out.println("状态: " + result.getStatus());

        switch (result.getStatus()) {
            case SUCCESS:
                System.out.println("✅ 支付成功!");
                System.out.println("渠道交易号: " + result.getChannelTransactionId());
                break;

            case PENDING:
                System.out.println("⏳ 支付处理中...");
                if (result.getCredential() != null) {
                    System.out.println("凭证类型: " + result.getCredential().getType());
                    System.out.println("凭证内容: " + result.getCredential().getContent());
                }
                break;

            case FAILED:
                System.out.println("❌ 支付失败!");
                if (result.getError() != null) {
                    System.out.println("错误码: " + result.getError().getCode());
                    System.out.println("错误信息: " + result.getError().getMessage());
                }
                break;

            default:
                System.out.println("❓ 未知状态: " + result.getStatus());
                break;
        }
    }

    /**
     * 展示新架构的优势
     */
    public void showNewArchitectureAdvantages() {
        System.out.println("=== 新架构优势 ===");

        System.out.println("1. 职责分离:");
        System.out.println("   - ChannelAdapter: 专注参数转换和特性发现");
        System.out.println("   - ChannelClient: 专注API调用和网络通信");
        System.out.println("   - PaymentClientService: 协调适配器和客户端");

        System.out.println("\n2. 更好的可测试性:");
        System.out.println("   - 可以单独测试参数转换逻辑");
        System.out.println("   - 可以单独测试API调用逻辑");
        System.out.println("   - 可以Mock客户端进行适配器测试");

        System.out.println("\n3. 更好的可维护性:");
        System.out.println("   - 参数格式变化只需修改Adapter");
        System.out.println("   - API调用方式变化只需修改Client");
        System.out.println("   - 业务逻辑变化只需修改Service");

        System.out.println("\n4. 更好的可扩展性:");
        System.out.println("   - 新增渠道只需实现对应的Adapter和Client");
        System.out.println("   - 支持同一个Client被多个Adapter使用");
        System.out.println("   - 支持动态注册和发现");

        System.out.println("\n5. 处理流程:");
        System.out.println("   请求 -> Service -> Adapter转换 -> Client调用API -> Adapter转换响应 -> 返回结果");
    }
}
