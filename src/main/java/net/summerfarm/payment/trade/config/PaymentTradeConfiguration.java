package net.summerfarm.payment.trade.config;

import net.summerfarm.payment.trade.adapter.ChannelAdapter;
import net.summerfarm.payment.trade.client.ChannelClient;
import net.summerfarm.payment.trade.service.impl.PaymentClientServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 支付交易配置类
 * 自动注册所有的适配器和客户端
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Configuration
public class PaymentTradeConfiguration {

    private static final Logger log = LoggerFactory.getLogger(PaymentTradeConfiguration.class);

    @Autowired
    private PaymentClientServiceImpl paymentClientService;

    @Autowired(required = false)
    private List<ChannelAdapter> channelAdapters;

    @Autowired(required = false)
    private List<ChannelClient> channelClients;

    /**
     * 自动注册所有的适配器和客户端
     */
    @PostConstruct
    public void registerChannelComponents() {
        log.info("开始注册支付渠道组件...");

        // 注册适配器
        if (channelAdapters != null && !channelAdapters.isEmpty()) {
            for (ChannelAdapter adapter : channelAdapters) {
                try {
                    paymentClientService.registerAdapter(adapter);
                } catch (Exception e) {
                    log.error("注册适配器失败: channelCode={}, error={}", 
                            adapter.getChannelCode(), e.getMessage(), e);
                }
            }
            log.info("成功注册 {} 个渠道适配器", channelAdapters.size());
        } else {
            log.warn("未找到任何渠道适配器");
        }

        // 注册客户端
        if (channelClients != null && !channelClients.isEmpty()) {
            for (ChannelClient client : channelClients) {
                try {
                    paymentClientService.registerClient(client);
                } catch (Exception e) {
                    log.error("注册客户端失败: channelCode={}, error={}", 
                            client.getChannelCode(), e.getMessage(), e);
                }
            }
            log.info("成功注册 {} 个渠道客户端", channelClients.size());
        } else {
            log.warn("未找到任何渠道客户端");
        }

        // 验证适配器和客户端的匹配性
        validateChannelComponents();

        log.info("支付渠道组件注册完成");
    }

    /**
     * 验证适配器和客户端的匹配性
     */
    private void validateChannelComponents() {
        if (channelAdapters == null || channelClients == null) {
            return;
        }

        for (ChannelAdapter adapter : channelAdapters) {
            String channelCode = adapter.getChannelCode();
            boolean hasMatchingClient = channelClients.stream()
                    .anyMatch(client -> channelCode.equals(client.getChannelCode()));
            
            if (!hasMatchingClient) {
                log.warn("适配器 {} 没有对应的客户端实现", channelCode);
            }
        }

        for (ChannelClient client : channelClients) {
            String channelCode = client.getChannelCode();
            boolean hasMatchingAdapter = channelAdapters.stream()
                    .anyMatch(adapter -> channelCode.equals(adapter.getChannelCode()));
            
            if (!hasMatchingAdapter) {
                log.warn("客户端 {} 没有对应的适配器实现", channelCode);
            }
        }
    }
}
