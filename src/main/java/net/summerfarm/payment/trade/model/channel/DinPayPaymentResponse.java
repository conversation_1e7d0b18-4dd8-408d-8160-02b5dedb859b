package net.summerfarm.payment.trade.model.channel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DinPay支付响应模型
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DinPayPaymentResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 商户订单号
     */
    private String orderNo;

    /**
     * DinPay订单号
     */
    private String dinpayOrderNo;

    /**
     * 支付链接
     */
    private String paymentUrl;

    /**
     * 二维码内容
     */
    private String qrCode;

    /**
     * 签名
     */
    private String sign;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 原始响应数据
     */
    private String rawData;
}
