package net.summerfarm.payment.gateway.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 商品详情信息
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户侧商品编码（必填）
     * 由半角的大小写字母、数字、中划线、下划线中的一种或几种组成
     */
    private String merchantGoodsId;

    /**
     * 微信支付商品编码（可选）
     * 微信支付定义的统一商品编号（没有可不传）
     */
    private String wechatpayGoodsId;

    /**
     * 商品名称（可选）
     * 商品的实际名称
     */
    private String goodsName;

    /**
     * 商品数量（必填）
     * 用户购买的数量
     */
    private Integer quantity;

    /**
     * 商品单价（必填）
     * 商品单价，单位为分
     */
    private Integer unitPrice;

    /**
     * 商品类目（可选）
     * 商品类目
     */
    private String goodsCategory;

    /**
     * 商品描述（可选）
     * 商品描述信息
     */
    private String body;

    /**
     * 商品的展示地址（可选）
     * 商品的展示地址
     */
    private String showUrl;
}
