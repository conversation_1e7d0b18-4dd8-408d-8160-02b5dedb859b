package net.summerfarm.payment.gateway.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.gateway.common.enums.PaymentStatus;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一支付响应模型
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedPaymentResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 支付状态
     */
    private PaymentStatus status;

    /**
     * 商户订单号
     */
    private String paymentNo;

    /**
     * SDK内部生成的支付单ID
     */
    private String paymentOrderId;

    /**
     * 渠道返回的交易流水号/订单号
     */
    private String channelTransactionId;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 支付时间（成功时才有）
     */
    private LocalDateTime paymentTime;

    /**
     * 支付凭证信息（如二维码、支付链接等）
     */
    private PaymentCredential credential;

    /**
     * 渠道返回的原始报文
     */
    private String rawResponse;

    /**
     * 错误信息（失败时才有）
     */
    private UnifiedError error;

    /**
     * 扩展信息
     */
    private Map<String, Object> extraInfo;

    /**
     * 处理时间戳
     */
    private Long timestamp;

    /**
     * 支付凭证信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PaymentCredential implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 凭证类型（QR_CODE: 二维码, REDIRECT_URL: 跳转链接, FORM_DATA: 表单数据等）
         */
        private String type;

        /**
         * 凭证内容（二维码内容、跳转URL、表单HTML等）
         */
        private String content;

        /**
         * 额外的凭证信息
         */
        private Map<String, Object> extraData;
    }

}
