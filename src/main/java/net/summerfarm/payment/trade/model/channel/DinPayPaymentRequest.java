package net.summerfarm.payment.trade.model.channel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DinPay支付请求模型
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DinPayPaymentRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 支付金额（分）
     */
    private Integer amount;

    /**
     * 商品描述
     */
    private String subject;

    /**
     * 附加数据
     */
    private String attach;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 异步通知地址
     */
    private String notifyUrl;

    /**
     * 同步返回地址
     */
    private String returnUrl;

    /**
     * 用户标识（openid等）
     */
    private String openid;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 签名
     */
    private String sign;

    /**
     * 时间戳
     */
    private Long timestamp;
}
