package net.summerfarm.payment.trade.client;

/**
 * 支付渠道客户端接口
 * 专注于与渠道API的具体交互，不负责参数转换
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
public interface ChannelClient {

    /**
     * 获取渠道标识符
     *
     * @return 渠道标识符
     */
    String getChannelCode();

    /**
     * 获取渠道名称
     *
     * @return 渠道名称
     */
    String getChannelName();

    /**
     * 执行支付API调用
     *
     * @param channelRequest 渠道特定的支付请求对象
     * @return 渠道原始响应对象
     * @throws Exception 当API调用失败时抛出异常
     */
    Object pay(Object channelRequest) throws Exception;

    /**
     * 执行退款API调用
     *
     * @param channelRequest 渠道特定的退款请求对象
     * @return 渠道原始响应对象
     * @throws Exception 当API调用失败时抛出异常
     */
    default Object refund(Object channelRequest) throws Exception {
        throw new UnsupportedOperationException("该渠道暂不支持退款功能");
    }

    /**
     * 执行查询API调用
     *
     * @param channelRequest 渠道特定的查询请求对象
     * @return 渠道原始响应对象
     * @throws Exception 当API调用失败时抛出异常
     */
    default Object query(Object channelRequest) throws Exception {
        throw new UnsupportedOperationException("该渠道暂不支持查询功能");
    }

    /**
     * 执行关单API调用
     *
     * @param channelRequest 渠道特定的关单请求对象
     * @return 渠道原始响应对象
     * @throws Exception 当API调用失败时抛出异常
     */
    default Object close(Object channelRequest) throws Exception {
        throw new UnsupportedOperationException("该渠道暂不支持关单功能");
    }

    /**
     * 健康检查 - 检查渠道API是否可用
     *
     * @return true表示渠道可用，false表示不可用
     */
    default boolean healthCheck() {
        return true;
    }

    /**
     * 获取API超时时间（毫秒）
     *
     * @return 超时时间
     */
    default int getTimeoutMillis() {
        return 30000; // 默认30秒
    }
}
