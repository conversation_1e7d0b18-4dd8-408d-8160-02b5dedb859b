package net.summerfarm.payment.trade.client;

/**
 * 支付渠道客户端接口
 * 专注于与渠道API的具体交互，不负责参数转换
 *
 * @param <REQ> 渠道特定的请求类型
 * @param <RESP> 渠道特定的响应类型
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
public interface ChannelClient<REQ, RESP> {

    /**
     * 获取渠道标识符
     *
     * @return 渠道标识符
     */
    String getChannelCode();

    /**
     * 获取渠道名称
     *
     * @return 渠道名称
     */
    String getChannelName();

    /**
     * 执行支付API调用
     *
     * @param channelRequest 渠道特定的支付请求对象
     * @return 渠道原始响应对象
     * @throws Exception 当API调用失败时抛出异常
     */
    RESP pay(REQ channelRequest) throws Exception;

    // TODO: 后续扩展退款、查询、关单等功能时，可以添加对应的泛型方法
    // 暂时保留默认实现，避免破坏现有接口

    /**
     * 健康检查 - 检查渠道API是否可用
     *
     * @return true表示渠道可用，false表示不可用
     */
    default boolean healthCheck() {
        return true;
    }

    /**
     * 获取API超时时间（毫秒）
     *
     * @return 超时时间
     */
    default int getTimeoutMillis() {
        return 30000; // 默认30秒
    }
}
