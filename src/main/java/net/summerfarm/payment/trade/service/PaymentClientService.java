package net.summerfarm.payment.trade.service;

import net.summerfarm.payment.trade.model.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.UnifiedPaymentResult;

/**
 * 统一支付客户端服务接口
 * 负责所有主动发起的交易操作
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
public interface PaymentClientService {

    /**
     * 发起支付
     * 
     * @param request 统一支付请求
     * @return 统一支付结果
     */
    UnifiedPaymentResult pay(UnifiedPaymentRequest request);

    // TODO: 后续扩展其他接口
    // UnifiedRefundResult refund(UnifiedRefundRequest request);
    // UnifiedQueryResult queryPayment(UnifiedQueryRequest request);
    // UnifiedQueryResult queryRefund(UnifiedQueryRequest request);
    // UnifiedCloseResult closeOrder(UnifiedCloseRequest request);
}
