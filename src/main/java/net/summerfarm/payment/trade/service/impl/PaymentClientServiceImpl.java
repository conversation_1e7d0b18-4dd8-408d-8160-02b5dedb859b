package net.summerfarm.payment.gateway.service.impl;

import net.summerfarm.payment.gateway.adapter.ChannelAdapter;
import net.summerfarm.payment.gateway.common.enums.ErrorCode;
import net.summerfarm.payment.gateway.common.enums.FeatureType;
import net.summerfarm.payment.gateway.common.exception.PaymentException;
import net.summerfarm.payment.gateway.model.UnifiedError;
import net.summerfarm.payment.gateway.model.UnifiedPaymentRequest;
import net.summerfarm.payment.gateway.model.UnifiedPaymentResult;
import net.summerfarm.payment.gateway.service.PaymentClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一支付客户端服务实现
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Service
public class PaymentClientServiceImpl implements PaymentClientService {

    private static final Logger log = LoggerFactory.getLogger(PaymentClientServiceImpl.class);

    /**
     * 渠道适配器注册表
     */
    private final Map<String, ChannelAdapter> adapterRegistry = new ConcurrentHashMap<>();

    /**
     * 注册渠道适配器
     *
     * @param adapter 渠道适配器
     */
    public void registerAdapter(ChannelAdapter adapter) {
        if (adapter == null) {
            throw new IllegalArgumentException("渠道适配器不能为空");
        }
        String channelCode = adapter.getChannelCode();
        if (channelCode == null || channelCode.trim().isEmpty()) {
            throw new IllegalArgumentException("渠道标识符不能为空");
        }

        adapterRegistry.put(channelCode, adapter);
        log.info("注册渠道适配器成功: channelCode={}, channelName={}, supportedFeatures={}",
                channelCode, adapter.getChannelName(), adapter.getSupportedFeatures());
    }

    @Override
    public UnifiedPaymentResult pay(UnifiedPaymentRequest request) {
        log.info("开始处理支付请求: outTradeNo={}, totalAmount={}, paymentMethod={}",
                request.getOutTradeNo(), request.getTotalAmount(), request.getPaymentMethod());

        try {
            // 1. 基础参数验证
            validatePaymentRequest(request);

            // 2. 获取渠道适配器
            String channelCode = request.getChannelConfig().getChannelCode();
            ChannelAdapter adapter = getChannelAdapter(channelCode);

            // 3. 特性检查
            validateFeatureSupport(request, adapter);

            // 4. 执行支付
            UnifiedPaymentResult result = adapter.doPay(request);

            log.info("支付请求处理完成: outTradeNo={}, status={}, channelTransactionId={}",
                    request.getOutTradeNo(), result.getStatus(), result.getChannelTransactionId());

            return result;

        } catch (PaymentException e) {
            String outTradeNo = request.getOutTradeNo();
            String channelName = request.getChannelConfig() != null
                    ? request.getChannelConfig().getChannelName() : "未知渠道";

            log.error("支付请求处理失败: outTradeNo={}, errorCode={}, message={}",
                    outTradeNo, e.getCode(), e.getMessage(), e);

            return UnifiedPaymentResult.failed(
                    outTradeNo,
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage()),
                    channelName
            );

        } catch (Exception e) {
            String outTradeNo = request.getOutTradeNo();
            String channelName = request.getChannelConfig() != null
                    ? request.getChannelConfig().getChannelName() : "未知渠道";

            log.error("支付请求处理异常: outTradeNo={}", outTradeNo, e);

            return UnifiedPaymentResult.failed(
                    outTradeNo,
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage()),
                    channelName
            );
        }
    }

    /**
     * 验证支付请求
     */
    private void validatePaymentRequest(UnifiedPaymentRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付请求不能为空");
        }
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }
        if (request.getChannelConfig().getChannelCode() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道标识符不能为空");
        }
    }

    /**
     * 获取渠道适配器
     */
    private ChannelAdapter getChannelAdapter(String channelCode) {
        ChannelAdapter adapter = adapterRegistry.get(channelCode);
        if (adapter == null) {
            throw new PaymentException(ErrorCode.CHANNEL_NOT_SUPPORTED,
                    "不支持的支付渠道: " + channelCode);
        }
        return adapter;
    }

    /**
     * 验证特性支持
     */
    private void validateFeatureSupport(UnifiedPaymentRequest request, ChannelAdapter adapter) {

        // 检查一分钱订单支持
        if (request.getTotalAmount() != null && request.getTotalAmount() == 1) {
            if (!adapter.supportsFeature(FeatureType.ALLOW_ONE_CENT_ORDER)) {
                throw new PaymentException(ErrorCode.CHANNEL_FEATURE_NOT_SUPPORTED,
                        "渠道不支持一分钱订单: " + adapter.getChannelName());
            }
        }
    }
}
