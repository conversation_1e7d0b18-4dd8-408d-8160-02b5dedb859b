package net.summerfarm.payment.trade.service.impl;

import net.summerfarm.payment.trade.adapter.ChannelAdapter;
import net.summerfarm.payment.trade.client.ChannelClient;
import net.summerfarm.payment.trade.common.enums.ErrorCode;
import net.summerfarm.payment.trade.common.enums.FeatureType;
import net.summerfarm.payment.trade.common.exception.PaymentException;
import net.summerfarm.payment.trade.model.common.UnifiedError;
import net.summerfarm.payment.trade.model.request.UnifiedPaymentRequest;
import net.summerfarm.payment.trade.model.response.UnifiedPaymentResult;
import net.summerfarm.payment.trade.service.PaymentClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一支付客户端服务实现
 *
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Service
public class PaymentClientServiceImpl implements PaymentClientService {

    private static final Logger log = LoggerFactory.getLogger(PaymentClientServiceImpl.class);

    /**
     * 渠道适配器注册表
     */
    private final Map<String, ChannelAdapter> adapterRegistry = new ConcurrentHashMap<>();

    /**
     * 渠道客户端注册表
     */
    private final Map<String, ChannelClient> clientRegistry = new ConcurrentHashMap<>();

    /**
     * 注册渠道适配器
     *
     * @param adapter 渠道适配器
     */
    public void registerAdapter(ChannelAdapter adapter) {
        if (adapter == null) {
            throw new IllegalArgumentException("渠道适配器不能为空");
        }
        String channelCode = adapter.getChannelCode();
        if (channelCode == null || channelCode.trim().isEmpty()) {
            throw new IllegalArgumentException("渠道标识符不能为空");
        }

        adapterRegistry.put(channelCode, adapter);
        log.info("注册渠道适配器成功: channelCode={}, channelName={}, supportedFeatures={}",
                channelCode, adapter.getChannelName(), adapter.getSupportedFeatures());
    }

    /**
     * 注册渠道客户端
     *
     * @param client 渠道客户端
     */
    public void registerClient(ChannelClient client) {
        if (client == null) {
            throw new IllegalArgumentException("渠道客户端不能为空");
        }
        String channelCode = client.getChannelCode();
        if (channelCode == null || channelCode.trim().isEmpty()) {
            throw new IllegalArgumentException("渠道标识符不能为空");
        }

        clientRegistry.put(channelCode, client);
        log.info("注册渠道客户端成功: channelCode={}, channelName={}",
                channelCode, client.getChannelName());
    }

    @Override
    public UnifiedPaymentResult pay(UnifiedPaymentRequest request) {
        try {
            // 1. 基础参数验证（最开始就验证，后续直接使用）
            validatePaymentRequest(request);

            log.info("开始处理支付请求: paymentNo={}, totalAmount={}, paymentMethod={}",
                    request.getPaymentNo(), request.getTotalAmount(), request.getPaymentMethod());

            // 2. 获取渠道适配器和客户端
            String channelCode = request.getChannelConfig().getChannelCode();
            ChannelAdapter adapter = getChannelAdapter(channelCode);
            ChannelClient client = getChannelClient(channelCode);

            // 3. 特性检查
            validateFeatureSupport(request, adapter);

            // 4. 转换请求参数
            Object channelRequest = adapter.convertPaymentRequest(request);

            // 5. 调用渠道API
            Object channelResponse = client.pay(channelRequest);

            // 6. 转换响应结果
            UnifiedPaymentResult result = adapter.convertPaymentResponse(channelResponse, request);

            log.info("支付请求处理完成: paymentNo={}, status={}, channelTransactionId={}",
                    request.getPaymentNo(), result.getStatus(), result.getChannelTransactionId());

            return result;

        } catch (PaymentException e) {
            // 参数已经在开始验证过，这里可以安全使用
            log.error("支付请求处理失败: paymentNo={}, errorCode={}, message={}",
                    request.getPaymentNo(), e.getCode(), e.getMessage(), e);

            return UnifiedPaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(e.getErrorCode(), e.getDetailMessage()),
                    request.getChannelConfig().getChannelName()
            );

        } catch (Exception e) {
            // 参数已经在开始验证过，这里可以安全使用
            log.error("支付请求处理异常: paymentNo={}", request.getPaymentNo(), e);

            return UnifiedPaymentResult.failed(
                    request.getPaymentNo(),
                    UnifiedError.from(ErrorCode.SYSTEM_ERROR, e.getMessage()),
                    request.getChannelConfig().getChannelName()
            );
        }
    }

    /**
     * 验证支付请求
     * 在最开始就做完整的参数验证，后续可以安全使用
     */
    private void validatePaymentRequest(UnifiedPaymentRequest request) {
        if (request == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付请求不能为空");
        }

        // 验证基础必填字段
        if (request.getPaymentNo() == null || request.getPaymentNo().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "商户订单号不能为空");
        }

        if (request.getTotalAmount() == null || request.getTotalAmount() <= 0) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付金额必须大于0");
        }

        if (request.getDescription() == null || request.getDescription().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "商品描述不能为空");
        }

        if (request.getPaymentMethod() == null || request.getPaymentMethod().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付方式不能为空");
        }

        if (request.getPlatform() == null || request.getPlatform().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "应用平台不能为空");
        }

        // 验证渠道配置
        if (request.getChannelConfig() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道配置不能为空");
        }

        if (request.getChannelConfig().getChannelCode() == null ||
            request.getChannelConfig().getChannelCode().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道标识符不能为空");
        }

        if (request.getChannelConfig().getChannelName() == null ||
            request.getChannelConfig().getChannelName().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "渠道名称不能为空");
        }

        // 验证支付者信息
        if (request.getPayer() == null) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付者信息不能为空");
        }

        if (request.getPayer().getUserId() == null || request.getPayer().getUserId().trim().isEmpty()) {
            throw new PaymentException(ErrorCode.INVALID_PARAMETER, "支付者用户ID不能为空");
        }
    }

    /**
     * 获取渠道适配器
     */
    private ChannelAdapter getChannelAdapter(String channelCode) {
        ChannelAdapter adapter = adapterRegistry.get(channelCode);
        if (adapter == null) {
            throw new PaymentException(ErrorCode.CHANNEL_NOT_SUPPORTED,
                    "不支持的支付渠道: " + channelCode);
        }
        return adapter;
    }

    /**
     * 获取渠道客户端
     */
    private ChannelClient getChannelClient(String channelCode) {
        ChannelClient client = clientRegistry.get(channelCode);
        if (client == null) {
            throw new PaymentException(ErrorCode.CHANNEL_NOT_SUPPORTED,
                    "未找到渠道客户端: " + channelCode);
        }
        return client;
    }

    /**
     * 验证特性支持
     */
    private void validateFeatureSupport(UnifiedPaymentRequest request, ChannelAdapter adapter) {

        // 检查一分钱订单支持
        if (request.getTotalAmount() != null && request.getTotalAmount() == 1) {
            if (!adapter.supportsFeature(FeatureType.ALLOW_ONE_CENT_ORDER)) {
                throw new PaymentException(ErrorCode.CHANNEL_FEATURE_NOT_SUPPORTED,
                        "渠道不支持一分钱订单: " + adapter.getChannelName());
            }
        }
    }
}
