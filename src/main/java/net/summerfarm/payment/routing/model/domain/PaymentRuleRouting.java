package net.summerfarm.payment.routing.model.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
* @description: 支付渠道路由
* @author: George
* @date: 2024-11-26
**/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRuleRouting {
    /**
    * 主键ID
    */
    private Long id;

    /**
    * 租户ID
    */
    private Long tenantId;

    /**
    * 业务线（summerfarm、pop、saas）
    */
    private String businessLine;

    /**
    * 路由字段（如area_no等）
    */
    private String routeKey;

    /**
    * 路由值
    */
    private String routeValue;

    /**
    * 支付规则主键ID
    */
    private Long ruleId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}