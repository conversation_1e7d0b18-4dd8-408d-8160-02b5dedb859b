package net.summerfarm.payment.routing.model.dto;

import lombok.Data;

import java.util.List;

/**
 * @description: 支付规则路由更新VO
 * @author: <PERSON>
 * @date: 2024-11-27
 **/
@Data
public class PaymentRuleRoutingUpdateDTO {

    /**
     * 支付规则主键id
     */
    private Long id;

    /**
     * 路由key
     * @see net.summerfarm.payment.routing.common.enums.PaymentRuleRoutingKeyEnums
     */
    private String routeKey;

    /**
     * 路由值
     */
    private List<String> routeValues;

    /**
     * 运营区域areaNos
     */
    private List<Long> areaNos;

    /**
     * 门店ids
     */
    private List<Long> mids;
}
