package net.summerfarm.payment.routing.model.params;

import lombok.Data;

import java.util.List;

/**
 * @description: 支付规则路由更新VO
 * @author: <PERSON>
 * @date: 2024-11-27
 **/
@Data
public class PaymentRuleRoutingUpdateParams {

    /**
     * 支付规则主键id
     */
    private Long id;

    /**
     * 运营区域areaNos
     */
    private List<Long> areaNos;

    /**
     * 门店ids
     */
    private List<Long> mids;
}
