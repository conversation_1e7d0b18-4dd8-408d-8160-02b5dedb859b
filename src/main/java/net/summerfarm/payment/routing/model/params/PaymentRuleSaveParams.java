package net.summerfarm.payment.routing.model.params;

import lombok.Data;

import java.util.List;

/**
 * @description: 支付规则保存输入VO
 * @author: <PERSON>
 * @date: 2024-11-27
 **/
@Data
public class PaymentRuleSaveParams {

    /**
     * 主键ID
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 应用平台（mini app、h5等）
     */
    private String platform;

    /**
     * 支付方式（微信、支付宝、二维码等）
     */
    private String paymentMethod;

    /**
     * 支付渠道id
     */
    private Long channelId;

    /**
     * 区域编号
     */
    private List<Long> areaNos;

    /**
     * 门店ids
     */
    private List<Long> mids;
}
