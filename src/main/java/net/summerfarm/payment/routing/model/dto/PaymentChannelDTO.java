package net.summerfarm.payment.routing.model.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description:
 * @author: George
 * @date: 2024-12-04
 **/
@Data
public class PaymentChannelDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 渠道名称（微信原生、招行间连等）
     */
    private String channelName;

    /**
     * 企业主体
     */
    private String companyEntity;

    /**
     * 操作人ID
     */
    private Long operatorAdminId;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

    /**
     * 状态（0-禁用，1-启用， 2-已删除）
     */
    private Integer status;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 子商户号
     */
    private String subMerchantNo;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 密钥
     */
    private String secret;

    /**
     * 额外信息（各渠道的特有参数，证书、收银员等）
     */
    private String extraConfig;
}
