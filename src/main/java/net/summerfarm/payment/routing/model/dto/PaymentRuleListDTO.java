package net.summerfarm.payment.routing.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 支付规则列表ViewObject
 * @author: George
 * @date: 2024-11-27
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentRuleListDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 支付方式（微信、支付宝、二维码等）
     */
    private String paymentMethod;

    /**
     * 应用平台（mini app、h5等）
     */
    private String platform;

    /**
     * 渠道名称（微信原生、招行间连等）
     */
    private String channelName;

    /**
     * 企业主体
     */
    private String companyEntity;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 渠道id
     */
    private Long channelId;
}
