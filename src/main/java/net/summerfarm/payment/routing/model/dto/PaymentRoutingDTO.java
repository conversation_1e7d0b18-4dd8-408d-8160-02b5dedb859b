package net.summerfarm.payment.routing.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-12-10
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentRoutingDTO {

    /**
     * 渠道code
     */
    private Integer channelCode;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 账户id
     */
    private Integer companyAccountId;

    /**
     * 销售主体名称
     */
    private String sellingEntityName;
}
