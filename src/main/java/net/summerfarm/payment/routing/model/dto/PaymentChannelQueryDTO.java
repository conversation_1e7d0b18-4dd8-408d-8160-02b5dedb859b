package net.summerfarm.payment.routing.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;

import java.util.List;

/**
 * @description: 支付渠道查询参数对象
 * @author: George
 * @date: 2024-11-27
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentChannelQueryDTO extends BasePageInput {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 精准查询渠道名称
     */
    private String preciseChannelName;

    /**
     * 企业主体
     */
    private String companyEntity;

    /**
     * 状态（0-禁用，1-启用， 2-已删除）
     */
    private Integer status;

    /**
     * 状态列表
     */
    private List<Integer> statusList;

    /**
     * 商户号
     */
    private String merchantNo;

}
