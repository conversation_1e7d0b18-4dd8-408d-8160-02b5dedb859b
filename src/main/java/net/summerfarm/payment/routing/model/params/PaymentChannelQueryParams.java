package net.summerfarm.payment.routing.model.params;

import lombok.Data;
import net.xianmu.common.input.BasePageInput;

/**
 * @description: 支付渠道查询参数对象
 * @author: <PERSON>
 * @date: 2024-11-27
 **/
@Data
public class PaymentChannelQueryParams extends BasePageInput {

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 企业主体
     */
    private String companyEntity;

    /**
     * 状态（0-禁用，1-启用， 2-已删除）
     */
    private Integer status;

    /**
     * 商户号
     */
    private String merchantNo;

}
