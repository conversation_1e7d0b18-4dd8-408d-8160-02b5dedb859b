package net.summerfarm.payment.routing.model.vo;

import lombok.Builder;
import lombok.Data;

/**
 * @description: 支付可用渠道ViewObject
 * @author: <PERSON>
 * @date: 2024-11-27
 **/
@Data
@Builder
public class PaymentUsableChannelVO {

    /**
     * 支付路由标识
     */
    private boolean routeVersionFlag;

    /**
     * 微信支付标识
     */
    private boolean wechatPayFlag;

    /**
     * 支付宝支付标识
     */
    private boolean alipayFlag;

    /**
     * 付款码支付标识
     */
    private boolean paymentCodeFlag;
}
