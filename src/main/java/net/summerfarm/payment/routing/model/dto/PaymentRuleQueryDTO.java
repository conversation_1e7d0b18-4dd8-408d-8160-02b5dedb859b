package net.summerfarm.payment.routing.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.xianmu.common.input.BasePageInput;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: <PERSON> 支付规则查询输入
 * @date: 2024-11-27
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRuleQueryDTO extends BasePageInput {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 应用平台（mini app、h5等）
     */
    private String platform;

    /**
     * 运营区域
     */
    private List<Long> areaNos;

    /**
     * id集合
     */
    private Collection<Long> ids;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 支付渠道id
     */
    private Long channelId;
}
