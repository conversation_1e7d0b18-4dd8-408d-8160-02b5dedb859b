package net.summerfarm.payment.routing.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description: 支付渠道明细ViewObject
 * @author: <PERSON>
 * @date: 2024-11-27
 **/
@Data
public class PaymentChannelDetailVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 渠道名称（微信原生、招行间连等）
     */
    private String channelName;

    /**
     * 企业主体
     */
    private String companyEntity;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 子商户号
     */
    private String subMerchantNo;
}
