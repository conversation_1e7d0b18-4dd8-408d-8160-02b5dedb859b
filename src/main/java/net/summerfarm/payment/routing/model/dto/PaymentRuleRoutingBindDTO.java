package net.summerfarm.payment.routing.model.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-12-02
 **/
@Data
@Builder
public class PaymentRuleRoutingBindDTO {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 应用平台（mini app、h5等）
     */
    private String platform;

    /**
     * 支付方式（微信、支付宝、二维码等）
     */
    private String paymentMethod;

    /**
     * 区域
     */
    private List<Long> areaNos;

    /**
     * 商户openid
     */
    private List<String> openIds;

    /**
     * 门店ids
     */
    private List<Long> mids;
}
