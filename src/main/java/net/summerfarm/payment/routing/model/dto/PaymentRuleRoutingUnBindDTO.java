package net.summerfarm.payment.routing.model.dto;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-01-18
 **/
@Data
public class PaymentRuleRoutingUnBindDTO {
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 路由键
     * @see net.summerfarm.payment.routing.common.enums.PaymentRuleRoutingKeyEnums
     */
    private String routeKey;

    /**
     * 路由值
     */
    private List<String> routeValue;
}
