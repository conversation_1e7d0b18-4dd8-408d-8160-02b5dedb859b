package net.summerfarm.payment.routing.dao;

import net.summerfarm.payment.routing.model.domain.PaymentChannel;
import net.summerfarm.payment.routing.model.dto.PaymentChannelCompanyEntityQueryDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
* @description: 支付渠道DAO接口
* @author: George
* @date: 2024-11-26
**/
public interface PaymentChannelDAO {
    /**
     * 根据主键删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(PaymentChannel record);

    /**
     * 选择性插入
     * @param record
     * @return
     */
    int insertSelective(PaymentChannel record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    PaymentChannel selectByPrimaryKey(Long id);

    /**
     * 根据主键选择性更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(PaymentChannel record);

    /**
     * 根据主键更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(PaymentChannel record);

    /**
     * 查询支付渠道列表
     * @param query
     * @return
     */
    List<PaymentChannel> listChannel(PaymentChannelQueryDTO query);

    /**
     * 根据唯一索引查询
     * @param tenantId
     * @param businessLine
     * @param channelName
     * @param companyEntity
     * @param merchantNo
     * @return
     */
    PaymentChannel selectByUnique(@Param("tenantId") Long tenantId, @Param("businessLine") String businessLine, @Param("channelName") String channelName, @Param("companyEntity") String companyEntity, @Param("merchantNo") String merchantNo);

    /**
     * 根据id集合查询
     * @param ids
     * @return
     */
    List<PaymentChannel> selectByIds(@Param("ids") Collection<Long> ids);

    /**
     * 查询企业主体
     * @param paymentChannelCompanyEntityQueryDTO
     * @return
     */
    List<String> queryCompanyEntities(PaymentChannelCompanyEntityQueryDTO paymentChannelCompanyEntityQueryDTO);
}