package net.summerfarm.payment.routing.dao;

import net.summerfarm.payment.routing.model.domain.PaymentRule;
import net.summerfarm.payment.routing.model.dto.PaymentRuleQueryDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRoutingQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
* @description: 支付规则DAO接口
* @author: George
* @date: 2024-11-26
**/
public interface PaymentRuleDAO {

    /**
     * 根据主键删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 插入
     * @param record
     * @return
     */
    int insert(PaymentRule record);

    /**
     * 选择性插入
     * @param record
     * @return
     */
    int insertSelective(PaymentRule record);

    /**
     * 根据主键查询
     * @param id
     * @return
     */
    PaymentRule selectByPrimaryKey(Long id);

    /**
     * 根据主键选择性更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(PaymentRule record);

    /**
     * 根据主键更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(PaymentRule record);

    /**
     * 根据渠道ID统计
     * @param id
     * @return
     */
    int countByChannelId(Long id);

    /**
     * 查询支付规则
     * @param query
     * @return
     */
    List<PaymentRule> listRule(PaymentRuleQueryDTO query);

    /**
     * 查询支付规则列表
     * @param ids
     * @return
     */
    List<PaymentRule> selectByIds(@Param("ids") Collection<Long> ids);

    /**
     * 查询可用的支付方式
     * @param paymentRoutingQueryDTO
     * @return
     */
    List<PaymentRule> queryUsableMethods(PaymentRoutingQueryDTO paymentRoutingQueryDTO);

    /**
     * 查询最早的支付规则
     * @param paymentRoutingQueryDTO
     * @return
     */
    PaymentRule selectEarliestRule(PaymentRoutingQueryDTO paymentRoutingQueryDTO);
}