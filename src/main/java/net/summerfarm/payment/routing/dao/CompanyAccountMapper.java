package net.summerfarm.payment.routing.dao;

import net.summerfarm.payment.routing.model.domain.CompanyAccount;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

/**
* @description: ${description}
* @author: George
* @date: 2024-12-10
**/
@Mapper
@Component("sdkCompanyAccountMapper")
public interface CompanyAccountMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CompanyAccount record);

    int insertSelective(CompanyAccount record);

    CompanyAccount selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CompanyAccount record);

    int updateByPrimaryKey(CompanyAccount record);
}