package net.summerfarm.payment.routing.service.impl;
import net.summerfarm.payment.routing.common.enums.*;
import net.summerfarm.payment.routing.dao.PaymentChannelDAO;
import net.summerfarm.payment.routing.dao.PaymentRuleDAO;
import net.summerfarm.payment.routing.dao.PaymentRuleRoutingDAO;
import net.summerfarm.payment.routing.model.domain.PaymentChannel;
import net.summerfarm.payment.routing.model.domain.PaymentRule;
import net.summerfarm.payment.routing.model.domain.PaymentRuleRouting;
import net.summerfarm.payment.routing.model.dto.*;
import net.summerfarm.payment.routing.service.PaymentRuleRoutingService;
import net.xianmu.common.exception.ParamsException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* @description: 支付规则路由服务类
* @author: George
* @date: 2024-11-26
**/
@Service
public class PaymentRuleRoutingServiceImpl implements PaymentRuleRoutingService {

    private static final Logger log = LoggerFactory.getLogger(PaymentRuleRoutingServiceImpl.class);
    @Resource
    private PaymentRuleDAO paymentRuleDAO;
    @Resource
    private PaymentChannelDAO paymentChannelDAO;
    @Resource
    private PaymentRuleRoutingDAO paymentRuleRoutingDAO;

    @Override
    public PaymentUsableMethodDTO queryUsableMethods(PaymentRoutingQueryDTO paymentRoutingQueryDTO) {
        validateQueryRoutingParams(paymentRoutingQueryDTO);
        List<PaymentRule> rules = paymentRuleDAO.queryUsableMethods(paymentRoutingQueryDTO);
        if (CollectionUtils.isEmpty(rules)) {
            return PaymentUsableMethodDTO.builder().build();
        }
        Map<String, PaymentRule> paymentMethodMap = rules.stream().collect(Collectors.toMap(PaymentRule::getPaymentMethod, item -> item, (v1, v2) -> v1));
        return PaymentUsableMethodDTO.builder()
                .wechatPayFlag(paymentMethodMap.containsKey(PaymentMethodEnums.WECHAT.getCode()))
                .alipayFlag(paymentMethodMap.containsKey(PaymentMethodEnums.ALIPAY.getCode()))
                .paymentCodeFlag(paymentMethodMap.containsKey(PaymentMethodEnums.PAYMENT_CODE.getCode()))
                .build();
    }

    private void validateQueryRoutingParams(PaymentRoutingQueryDTO paymentRoutingQueryDTO) {
        if (paymentRoutingQueryDTO == null) {
            throw new ParamsException("查询参数不能为空");
        }
        if (paymentRoutingQueryDTO.getTenantId() == null) {
            throw new ParamsException("租户ID不能为空");
        }
        if (!Objects.equals(paymentRoutingQueryDTO.getTenantId(), TenantIdEnums.SUMMER_FARM.getTenantId())) {
            throw new ParamsException("暂时只支持鲜沐商城业务线查询");
        }
        if (StringUtils.isEmpty(paymentRoutingQueryDTO.getBusinessLine())) {
            throw new ParamsException("业务线不能为空");
        }
        if (StringUtils.isEmpty(paymentRoutingQueryDTO.getPlatform())) {
            throw new ParamsException("平台不能为空");
        }
        if (StringUtils.isEmpty(paymentRoutingQueryDTO.getRouteKey())) {
            throw new ParamsException("路由键不能为空");
        }
        if (StringUtils.isEmpty(paymentRoutingQueryDTO.getRouteValue())) {
            throw new ParamsException("路由值不能为空");
        }
    }

    @Override
    public PaymentRoutingDTO getRoutingInfo(PaymentRoutingQueryDTO paymentRoutingQueryDTO) {
        validateGetRoutingParams(paymentRoutingQueryDTO);

        // 优先根据b2b门店助手认证openid路由
        PaymentChannel b2bChannel = getRouteChannelByB2b(paymentRoutingQueryDTO);
        if (b2bChannel != null) {
            return buildPaymentRoutingDTO(b2bChannel);
        }

        // 其次根据mid白名单路由
        PaymentChannel mIdChannel = getRouteChannelByMid(paymentRoutingQueryDTO);
        if (mIdChannel != null) {
            return buildPaymentRoutingDTO(mIdChannel);
        }

        // 区域路由
        PaymentChannel areaChannel = getRouteChannelByArea(paymentRoutingQueryDTO);
        return buildPaymentRoutingDTO(areaChannel);
    }

    private PaymentChannel getRouteChannelByMid(PaymentRoutingQueryDTO paymentRoutingQueryDTO) {
        if (paymentRoutingQueryDTO.getMId() == null) {
            return null;
        }

        PaymentRoutingQueryDTO dto = PaymentRoutingQueryDTO.builder()
                .tenantId(paymentRoutingQueryDTO.getTenantId())
                .businessLine(paymentRoutingQueryDTO.getBusinessLine())
                .platform(paymentRoutingQueryDTO.getPlatform())
                .paymentMethod(paymentRoutingQueryDTO.getPaymentMethod())
                .routeKey(PaymentRuleRoutingKeyEnums.MID.getKey())
                .routeValue(String.valueOf(paymentRoutingQueryDTO.getMId()))
                .build();

        List<PaymentRule> rules = paymentRuleDAO.queryUsableMethods(dto);
        if (CollectionUtils.isEmpty(rules)) {
            return null;
        }
        PaymentRule paymentRule = rules.get(0);
        return paymentChannelDAO.selectByPrimaryKey(paymentRule.getChannelId());
    }

    private PaymentChannel getRouteChannelByArea(PaymentRoutingQueryDTO paymentRoutingQueryDTO) {
        List<PaymentRule> rules = paymentRuleDAO.queryUsableMethods(paymentRoutingQueryDTO);
        if (CollectionUtils.isEmpty(rules)) {
            return null;
        }
        PaymentRule paymentRule = rules.get(0);
        return paymentChannelDAO.selectByPrimaryKey(paymentRule.getChannelId());
    }

    private PaymentRoutingDTO buildPaymentRoutingDTO(PaymentChannel channel) {
        if (channel == null) {
            return PaymentRoutingDTO.builder().build();
        }
        return PaymentRoutingDTO.builder()
                .channelCode(PaymentChannelProviderEnums.getCodeByName(channel.getChannelName()))
                .channelId(channel.getId())
                .companyAccountId(channel.getCompanyAccountId())
                .sellingEntityName(channel.getCompanyEntity())
                .build();
    }

    private PaymentChannel getRouteChannelByB2b(PaymentRoutingQueryDTO paymentRoutingQueryDTO) {
        // 临时用于B2B路由开关关闭
        if (Objects.equals(paymentRoutingQueryDTO.getB2bSwitch(), PaymentB2bSwitchEnums.CLOSE.getCode())) {
            return null;
        }
        // 小程序优先根据门店助手b2b授权openid进行路由
        if (!Objects.equals(paymentRoutingQueryDTO.getPaymentMethod(), PaymentMethodEnums.WECHAT.getCode()) || !Objects.equals(paymentRoutingQueryDTO.getPlatform(), PaymentDictionaryEnums.Platform.MINI_APP.getName())) {
            return null;
        }
        if (StringUtils.isEmpty(paymentRoutingQueryDTO.getOpenId())) {
            return null;
        }
        PaymentRoutingQueryDTO dto = PaymentRoutingQueryDTO.builder()
                .tenantId(paymentRoutingQueryDTO.getTenantId())
                .businessLine(paymentRoutingQueryDTO.getBusinessLine())
                .platform(paymentRoutingQueryDTO.getPlatform())
                .paymentMethod(paymentRoutingQueryDTO.getPaymentMethod())
                .routeKey(PaymentRuleRoutingKeyEnums.B2B_AUTH_OPEN_ID.getKey())
                .routeValue(paymentRoutingQueryDTO.getOpenId())
                .build();

        List<PaymentRule> rules = paymentRuleDAO.queryUsableMethods(dto);
        if (CollectionUtils.isEmpty(rules)) {
           return null;
        }
        PaymentRule paymentRule = rules.get(0);
        Long channelId = paymentRule.getChannelId();
        if (StringUtils.isEmpty(paymentRoutingQueryDTO.getSellingEntityName())) {
            return paymentChannelDAO.selectByPrimaryKey(channelId);
        }
        PaymentChannelQueryDTO channelQueryDTO = PaymentChannelQueryDTO.builder()
                .tenantId(paymentRoutingQueryDTO.getTenantId())
                .businessLine(paymentRoutingQueryDTO.getBusinessLine())
                .companyEntity(paymentRoutingQueryDTO.getSellingEntityName())
                .preciseChannelName(PaymentChannelProviderEnums.WECHAT_B2B.getChannelName())
                .status(PaymentChannelStatusEnums.NORMAL.getStatus())
                .build();
        List<PaymentChannel> paymentChannels = paymentChannelDAO.listChannel(channelQueryDTO);
        if (CollectionUtils.isEmpty(paymentChannels)) {
            return paymentChannelDAO.selectByPrimaryKey(channelId);
        }
        return paymentChannels.get(0);
    }

    private void validateGetRoutingParams(PaymentRoutingQueryDTO paymentRoutingQueryDTO) {
        validateQueryRoutingParams(paymentRoutingQueryDTO);
        if (StringUtils.isEmpty(paymentRoutingQueryDTO.getPaymentMethod())) {
            throw new ParamsException("支付方式不能为空");
        }
    }

    @Override
    public void unbindB2bAuthOpenId(PaymentRuleRoutingUnBindDTO unBindDTO) {
        validateUnbindB2bAuthOpenIdParams(unBindDTO);
        PaymentRuleRoutingQueryDTO queryRoutingDTO = PaymentRuleRoutingQueryDTO.builder()
                .tenantId(unBindDTO.getTenantId())
                .businessLine(unBindDTO.getBusinessLine())
                .routeKey(unBindDTO.getRouteKey())
                .routeValue(unBindDTO.getRouteValue())
                .build();
        List<PaymentRuleRouting> routingList = paymentRuleRoutingDAO.listRuleRouting(queryRoutingDTO);
        if (CollectionUtils.isEmpty(routingList)) {
            log.info("未查询到需要解绑的支付规则路由");
            return;
        }
        log.info("解绑支付规则路由:{}", routingList);
        paymentRuleRoutingDAO.deleteByIds(routingList.stream().map(PaymentRuleRouting::getId).collect(Collectors.toList()));
    }

    private void validateUnbindB2bAuthOpenIdParams(PaymentRuleRoutingUnBindDTO unBindDTO) {
        if (unBindDTO == null) {
            throw new ParamsException("解绑参数不能为空");
        }
        if (unBindDTO.getTenantId() == null) {
            throw new ParamsException("租户ID不能为空");
        }
        if (StringUtils.isEmpty(unBindDTO.getBusinessLine())) {
            throw new ParamsException("业务线不能为空");
        }
        if (StringUtils.isEmpty(unBindDTO.getRouteKey())) {
            throw new ParamsException("路由键不能为空");
        }
        if (CollectionUtils.isEmpty(unBindDTO.getRouteValue())) {
            throw new ParamsException("路由值不能为空");
        }
    }
}
