package net.summerfarm.payment.routing.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.payment.routing.common.convertor.PageInfoConvertor;
import net.summerfarm.payment.routing.common.convertor.PaymentChannelConvertor;
import net.summerfarm.payment.routing.common.enums.*;
import net.summerfarm.payment.routing.dao.CompanyAccountMapper;
import net.summerfarm.payment.routing.dao.PaymentChannelDAO;
import net.summerfarm.payment.routing.dao.PaymentRuleDAO;
import net.summerfarm.payment.routing.model.domain.CompanyAccount;
import net.summerfarm.payment.routing.model.domain.PaymentChannel;
import net.summerfarm.payment.routing.model.domain.PaymentRule;
import net.summerfarm.payment.routing.model.dto.*;
import net.summerfarm.payment.routing.service.PaymentChannelService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * @description: 支付渠道服务实现类
 * @author: George
 * @date: 2024-11-26
 **/
@Service
@Slf4j
public class PaymentChannelServiceImpl implements PaymentChannelService {

    @Resource
    private PaymentChannelDAO paymentChannelDAO;
    @Resource
    private PaymentRuleDAO paymentRuleDAO;
    @Autowired
    @Qualifier("sdkCompanyAccountMapper")
    private CompanyAccountMapper companyAccountMapper;

    @Override
    public PageInfo<PaymentChannelListDTO> pageListChannel(PaymentChannelQueryDTO query) {
        validatePageListParams(query);
        PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        List<PaymentChannel> paymentChannels = paymentChannelDAO.listChannel(query);
        PageInfo<PaymentChannel> pageInfo = PageInfoHelper.createPageInfo(paymentChannels);
        return PageInfoConvertor.convert2PageInfoDTO(pageInfo, PaymentChannelConvertor::convert2ListDTO);
    }

    private void validatePageListParams(PaymentChannelQueryDTO query) {
        if (query == null) {
            throw new ParamsException("查询条件不能为空");
        }
        if (query.getPageIndex() == null || query.getPageSize() == null) {
            throw new ParamsException("分页参数不能为空");
        }
        if (query.getTenantId() == null) {
            throw new ParamsException("租户ID不能为空");
        }
        if (StringUtils.isEmpty(query.getBusinessLine())) {
            throw new ParamsException("业务线不能为空");
        }
    }

    @Override
    public List<PaymentChannelListDTO> listChannel(PaymentChannelQueryDTO query) {
        return PaymentChannelConvertor.convert2ListDTO(paymentChannelDAO.listChannel(query));
    }

    @Override
    public PaymentChannelDetailDTO queryChannelDetail(PaymentChannelIdDTO paymentChannelIdDTO) {
        validateQueryDetailParams(paymentChannelIdDTO);
        PaymentChannel channel = paymentChannelDAO.selectByPrimaryKey(paymentChannelIdDTO.getId());
        return PaymentChannelConvertor.convert2DetailDTO(channel);
    }

    private void validateQueryDetailParams(PaymentChannelIdDTO paymentChannelIdDTO) {
        if (paymentChannelIdDTO == null) {
            throw new ParamsException("查询条件不能为空");
        }
        if (paymentChannelIdDTO.getId() == null) {
            throw new ParamsException("支付渠道ID不能为空");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveChannel(PaymentChannelSaveDTO paymentChannelSaveDTO) {
        validateSaveChannelParams(paymentChannelSaveDTO);
        // 校验渠道唯一性（租户 + 业务线 + 渠道名称 + 企业主体 + 商户号）
        validateUniqueChannel(paymentChannelSaveDTO);
        PaymentChannel paymentChannel = buildChannel(paymentChannelSaveDTO);
        if (paymentChannel.getId() != null) {
            // 更新
            log.info("检测到已存在被删除的支付渠道，即将执行更新操作，参数:{}", paymentChannelSaveDTO);
            paymentChannelDAO.updateByPrimaryKeySelective(paymentChannel);
            return paymentChannel.getId();
        }
        // 同步写account
        CompanyAccount account = buildCompanyAccount(paymentChannelSaveDTO);
        companyAccountMapper.insert(account);

        paymentChannel.setCompanyAccountId(account.getId());
        paymentChannelDAO.insert(paymentChannel);

        // 如果是微信B2B支付渠道 初始化支付规则
        initPaymentRuleIfWechatB2B(paymentChannelSaveDTO, paymentChannel.getId());
        return paymentChannel.getId();
    }

    private void initPaymentRuleIfWechatB2B(PaymentChannelSaveDTO paymentChannelSaveDTO, Long channelId) {
        if (!Objects.equals(paymentChannelSaveDTO.getChannelName(), PaymentChannelProviderEnums.WECHAT_B2B.getChannelName())) {
            return;
        }
        PaymentRule paymentRule = new PaymentRule();
        paymentRule.setTenantId(paymentChannelSaveDTO.getTenantId());
        paymentRule.setBusinessLine(paymentChannelSaveDTO.getBusinessLine());
        paymentRule.setSceneName("小程序微信B2B支付");
        paymentRule.setPlatform(PaymentDictionaryEnums.Platform.MINI_APP.getName());
        paymentRule.setPaymentMethod(PaymentMethodEnums.WECHAT.getCode());
        paymentRule.setChannelId(channelId);
        paymentRuleDAO.insert(paymentRule);
    }

    private CompanyAccount buildCompanyAccount(PaymentChannelSaveDTO paymentChannelSaveDTO) {
        String channelName = paymentChannelSaveDTO.getChannelName();
        JSONObject paymentInfoJson = new JSONObject();
        if (Objects.equals(channelName, PaymentDictionaryEnums.ChannelName.WECHAT.getName())) {
            paymentInfoJson.put("wxMchId", paymentChannelSaveDTO.getMerchantNo());
            paymentInfoJson.put("wxMchKey", paymentChannelSaveDTO.getSecret());
            paymentInfoJson.put("wxCertPath", paymentChannelSaveDTO.getCertPath());
        }
        if (Objects.equals(channelName, PaymentDictionaryEnums.ChannelName.CMB.getName())) {
            paymentInfoJson.put("merId", paymentChannelSaveDTO.getMerchantNo());
            paymentInfoJson.put("pubKey", paymentChannelSaveDTO.getPublicKey());
            paymentInfoJson.put("priKey", paymentChannelSaveDTO.getPrivateKey());
            paymentInfoJson.put("secret", paymentChannelSaveDTO.getAppSecret());
            paymentInfoJson.put("userId", paymentChannelSaveDTO.getUserId());
            paymentInfoJson.put("appId", paymentChannelSaveDTO.getAppId());
        }
        if (Objects.equals(channelName, PaymentDictionaryEnums.ChannelName.B2B_WECHAT_FIRE_FACE.getName())) {
            paymentInfoJson.put("merchantNo", paymentChannelSaveDTO.getMerchantNo());
            paymentInfoJson.put("authCode", paymentChannelSaveDTO.getAuthCode());
            paymentInfoJson.put("refundPassword", paymentChannelSaveDTO.getRefundPassword());
            paymentInfoJson.put("operatorAccount", paymentChannelSaveDTO.getOperatorAccount());
            paymentInfoJson.put("secret", paymentChannelSaveDTO.getSecret());
        }
        if (Objects.equals(channelName, PaymentDictionaryEnums.ChannelName.DIN_PAY.getName())) {
            paymentInfoJson.put("merchantNo", paymentChannelSaveDTO.getMerchantNo());
            paymentInfoJson.put("privateKey", paymentChannelSaveDTO.getPrivateKey());
            paymentInfoJson.put("publicKey", paymentChannelSaveDTO.getPublicKey());
            paymentInfoJson.put("secret", paymentChannelSaveDTO.getSecret());
        }

        return CompanyAccount.builder()
                .companyName(paymentChannelSaveDTO.getCompanyEntity())
                .wxAccountInfo(paymentInfoJson.toJSONString())
                .adminId(paymentChannelSaveDTO.getOperatorAdminId().intValue())
                .addtime(LocalDateTime.now())
                .channel(PaymentDictionaryEnums.ChannelName.getCodeByName(channelName))
                .build();
    }

    private void validateUniqueChannel(PaymentChannelSaveDTO paymentChannelSaveDTO) {
        PaymentChannel channel = paymentChannelDAO.selectByUnique(paymentChannelSaveDTO.getTenantId(), paymentChannelSaveDTO.getBusinessLine(), paymentChannelSaveDTO.getChannelName(), paymentChannelSaveDTO.getCompanyEntity(), paymentChannelSaveDTO.getMerchantNo());
        if (channel == null) {
            return;
        }
        if (PaymentChannelStatusEnums.getExistStatus().contains(channel.getStatus())) {
            throw new BizException("当前支付渠道已存在");
        }
        if (Objects.equals(channel.getStatus(), PaymentChannelStatusEnums.DELETED.getStatus())) {
            paymentChannelSaveDTO.setId(channel.getId());
        }
    }

    private PaymentChannel buildChannel(PaymentChannelSaveDTO paymentChannelSaveDTO) {
        String extraConfig = getExtraConfig(paymentChannelSaveDTO);
        return PaymentChannel.builder()
                .id(paymentChannelSaveDTO.getId())
                .tenantId(paymentChannelSaveDTO.getTenantId())
                .businessLine(paymentChannelSaveDTO.getBusinessLine())
                .channelName(paymentChannelSaveDTO.getChannelName())
                .companyEntity(paymentChannelSaveDTO.getCompanyEntity())
                .merchantNo(paymentChannelSaveDTO.getMerchantNo())
                .publicKey(paymentChannelSaveDTO.getPublicKey())
                .privateKey(paymentChannelSaveDTO.getPrivateKey())
                .secret(paymentChannelSaveDTO.getSecret())
                .extraConfig(extraConfig)
                .status(PaymentChannelStatusEnums.NORMAL.getStatus())
                .operatorAdminId(paymentChannelSaveDTO.getOperatorAdminId())
                .operateTime(LocalDateTime.now())
                .build();
    }

    private String getExtraConfig(PaymentChannelSaveDTO paymentChannelSaveDTO) {
        JSONObject extraConfig = new JSONObject();
        if (Objects.equals(paymentChannelSaveDTO.getChannelName(), PaymentChannelProviderEnums.WECHAT_NATIVE.getChannelName())) {
            extraConfig.put(PaymentChannelExtraConfigFiledEnums.CERT_PATH.getFieldName(), paymentChannelSaveDTO.getCertPath());
        }
        if (Objects.equals(paymentChannelSaveDTO.getChannelName(), PaymentChannelProviderEnums.CMB.getChannelName())) {
            extraConfig.put(PaymentChannelExtraConfigFiledEnums.USERID.getFieldName(), paymentChannelSaveDTO.getUserId());
            extraConfig.put(PaymentChannelExtraConfigFiledEnums.APPID.getFieldName(), paymentChannelSaveDTO.getAppId());
            extraConfig.put(PaymentChannelExtraConfigFiledEnums.APP_SECRET.getFieldName(), paymentChannelSaveDTO.getAppSecret());
        }
        if (Objects.equals(paymentChannelSaveDTO.getChannelName(), PaymentChannelProviderEnums.WECHAT_B2B.getChannelName())) {
            extraConfig.put(PaymentChannelExtraConfigFiledEnums.AUTH_CODE.getFieldName(), paymentChannelSaveDTO.getAuthCode());
            extraConfig.put(PaymentChannelExtraConfigFiledEnums.REFUND_PASSWORD.getFieldName(), paymentChannelSaveDTO.getRefundPassword());
            extraConfig.put(PaymentChannelExtraConfigFiledEnums.OPERATOR_ACCOUNT.getFieldName(), paymentChannelSaveDTO.getOperatorAccount());
        }
        return extraConfig.toString();
    }

    private void validateSaveChannelParams(PaymentChannelSaveDTO paymentChannelSaveDTO) {
        if (paymentChannelSaveDTO == null) {
            throw new ParamsException("保存参数不能为空");
        }
        if (paymentChannelSaveDTO.getTenantId() == null) {
            throw new ParamsException("租户ID不能为空");
        }
        if (StringUtils.isEmpty(paymentChannelSaveDTO.getBusinessLine())) {
            throw new ParamsException("业务线不能为空");
        }
        if (StringUtils.isEmpty(paymentChannelSaveDTO.getChannelName())) {
            throw new ParamsException("渠道名称不能为空");
        }
        if (PaymentChannelProviderEnums.getCodeByName(paymentChannelSaveDTO.getChannelName()) == null) {
            throw new ParamsException("渠道名称不合法");
        }
        if (StringUtils.isEmpty(paymentChannelSaveDTO.getCompanyEntity())) {
            throw new ParamsException("企业主体不能为空");
        }
        if (paymentChannelSaveDTO.getCompanyEntity().length() > 50) {
            throw new ParamsException("企业主体长度不能超过50");
        }
        if (StringUtils.isEmpty(paymentChannelSaveDTO.getMerchantNo())) {
            throw new ParamsException("商户号不能为空");
        }
        if (paymentChannelSaveDTO.getMerchantNo().length() > 50) {
            throw new ParamsException("商户号长度不能超过50");
        }
        if (paymentChannelSaveDTO.getOperatorAdminId() == null) {
            throw new ParamsException("操作人ID不能为空");
        }
        // 微信原生校验 商户号、密钥、证书
        if (Objects.equals(paymentChannelSaveDTO.getChannelName(), PaymentChannelProviderEnums.WECHAT_NATIVE.getChannelName())) {
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getSecret())) {
                throw new ParamsException("商户密钥不能为空");
            }
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getCertPath())) {
                throw new ParamsException("证书路径不能为空");
            }
        }
        // 招行三方支付校验 商户号、公私钥、密钥、收银员id、appid、appsecret
        if (Objects.equals(paymentChannelSaveDTO.getChannelName(), PaymentChannelProviderEnums.CMB.getChannelName())) {
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getPublicKey())) {
                throw new ParamsException("公钥不能为空");
            }
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getPrivateKey())) {
                throw new ParamsException("私钥不能为空");
            }
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getUserId())) {
                throw new ParamsException("收银员ID不能为空");
            }
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getAppId())) {
                throw new ParamsException("APPID不能为空");
            }
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getAppSecret())) {
                throw new ParamsException("APP SECRET不能为空");
            }
        }
        // 微信B2B支付校验
        if (Objects.equals(paymentChannelSaveDTO.getChannelName(), PaymentChannelProviderEnums.WECHAT_B2B.getChannelName())) {
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getSecret())) {
                throw new ParamsException("商户密钥不能为空");
            }
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getAuthCode())) {
                throw new ParamsException("授权码不能为空");
            }
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getRefundPassword())) {
                throw new ParamsException("退款密码不能为空");
            }
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getOperatorAccount())) {
                throw new ParamsException("操作员账号不能为空");
            }
        }
        if (Objects.equals(paymentChannelSaveDTO.getChannelName(), PaymentChannelProviderEnums.DIN_PAY.getChannelName())) {
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getSecret())) {
                throw new ParamsException("商户密钥不能为空");
            }
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getPrivateKey())) {
                throw new ParamsException("商户私钥不能为空");
            }
            if (StringUtils.isEmpty(paymentChannelSaveDTO.getPublicKey())) {
                throw new ParamsException("平台公钥不能为空");
            }
        }
    }

    @Override
    public boolean changeStatus(PaymentChannelStatusDTO paymentChannelStatusDTO) {
        validateChangeStatusParams(paymentChannelStatusDTO);
        PaymentChannel channel = paymentChannelDAO.selectByPrimaryKey(paymentChannelStatusDTO.getId());
        if (channel == null) {
            throw new BizException("支付渠道不存在");
        }
        if (PaymentChannelStatusEnums.getAbnormalStatus().contains(paymentChannelStatusDTO.getStatus())) {
            // 禁用、删除操作，检查下是否有规则正在引用该支付渠道
            int count = paymentRuleDAO.countByChannelId(paymentChannelStatusDTO.getId());
            if (count > 0) {
                throw new BizException("当前商户已关联支付场景，不可删除，请先解绑");
            }
        }
        PaymentChannel updateChannel = PaymentChannel.builder()
                .id(paymentChannelStatusDTO.getId())
                .status(paymentChannelStatusDTO.getStatus())
                .operatorAdminId(paymentChannelStatusDTO.getOperatorAdminId())
                .operateTime(LocalDateTime.now())
                .build();
        return paymentChannelDAO.updateByPrimaryKeySelective(updateChannel) > 0;
    }

    private void validateChangeStatusParams(PaymentChannelStatusDTO paymentChannelStatusDTO) {
        if (paymentChannelStatusDTO == null) {
            throw new ParamsException("状态参数不能为空");
        }
        if (paymentChannelStatusDTO.getId() == null) {
            throw new ParamsException("支付渠道ID不能为空");
        }
        if (paymentChannelStatusDTO.getStatus() == null) {
            throw new ParamsException("状态不能为空");
        }
        if (Arrays.stream(PaymentChannelStatusEnums.values()).noneMatch(el -> Objects.equals(paymentChannelStatusDTO.getStatus(), el.getStatus()))) {
            throw new ParamsException("状态不合法");
        }
    }

    @Override
    public List<String> queryCompanyEntities(PaymentChannelCompanyEntityQueryDTO paymentChannelCompanyEntityQueryDTO) {
        return paymentChannelDAO.queryCompanyEntities(paymentChannelCompanyEntityQueryDTO);
    }

    @Override
    public PaymentChannelTiledDTO selectById(Long id) {
        if (id == null) {
            throw new ParamsException("支付渠道ID不能为空");
        }
        PaymentChannel paymentChannel = paymentChannelDAO.selectByPrimaryKey(id);
        return PaymentChannelConvertor.convert2TiledDTO(paymentChannel);
    }
}
