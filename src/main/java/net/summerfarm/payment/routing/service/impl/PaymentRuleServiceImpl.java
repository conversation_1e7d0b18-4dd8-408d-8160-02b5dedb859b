package net.summerfarm.payment.routing.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.payment.routing.common.convertor.PageInfoConvertor;
import net.summerfarm.payment.routing.common.convertor.PaymentRuleConvertor;
import net.summerfarm.payment.routing.common.enums.*;
import net.summerfarm.payment.routing.dao.PaymentChannelDAO;
import net.summerfarm.payment.routing.dao.PaymentRuleDAO;
import net.summerfarm.payment.routing.dao.PaymentRuleRoutingDAO;
import net.summerfarm.payment.routing.model.domain.PaymentChannel;
import net.summerfarm.payment.routing.model.domain.PaymentRule;
import net.summerfarm.payment.routing.model.domain.PaymentRuleRouting;
import net.summerfarm.payment.routing.model.dto.*;
import net.summerfarm.payment.routing.service.PaymentRuleService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
* @description: 支付规则服务实现类
* @author: George
* @date: 2024-11-26
**/
@Service
public class PaymentRuleServiceImpl implements PaymentRuleService {

    private static final Logger log = LoggerFactory.getLogger(PaymentRuleServiceImpl.class);
    @Resource
    private PaymentRuleDAO paymentRuleDAO;
    @Resource
    private PaymentRuleRoutingDAO paymentRuleRoutingDAO;
    @Resource
    private PaymentChannelDAO paymentChannelDAO;

    @Override
    public PageInfo<PaymentRuleListDTO> pageListRule(PaymentRuleQueryDTO query) {
        validatePageListParams(query);
        boolean continueQueryFlag = assemblyPageListParams(query);
        if (!continueQueryFlag) {
            return PageInfoHelper.createPageInfo(Collections.emptyList());
        }
        PageHelper.startPage(query.getPageIndex(), query.getPageSize());
        List<PaymentRule> paymentRules = paymentRuleDAO.listRule(query);
        if (CollectionUtils.isEmpty(paymentRules)) {
            return PageInfoHelper.createPageInfo(Collections.emptyList());
        }

        PageInfo<PaymentRule> pageInfo = PageInfoHelper.createPageInfo(paymentRules);
        PageInfo<PaymentRuleListDTO> convertedPageInfo = PageInfoConvertor.convert2PageInfoDTO(pageInfo, PaymentRuleConvertor::convert2ListDTO);
        Set<Long> channelIds = paymentRules.stream().map(PaymentRule::getChannelId).collect(Collectors.toSet());
        List<PaymentChannel> channels = paymentChannelDAO.selectByIds(channelIds);
        Map<Long, PaymentChannel> channelMap = channels.stream().collect(Collectors.toMap(PaymentChannel::getId, item -> item));
        return buildResponse(convertedPageInfo, channelMap);
    }

    private PageInfo<PaymentRuleListDTO> buildResponse(PageInfo<PaymentRuleListDTO> pageInfo, Map<Long, PaymentChannel> channelMap) {
        List<PaymentRuleListDTO> list = pageInfo.getList();
        for (PaymentRuleListDTO paymentRuleListDTO : list) {
            PaymentChannel channel = channelMap.get(paymentRuleListDTO.getChannelId());
            paymentRuleListDTO.setChannelName(channel.getChannelName());
            paymentRuleListDTO.setCompanyEntity(channel.getCompanyEntity());
            paymentRuleListDTO.setMerchantNo(channel.getMerchantNo());
        }
        return pageInfo;
    }

    private boolean assemblyPageListParams(PaymentRuleQueryDTO query) {
        List<Long> areaNos = query.getAreaNos();
        if (!CollectionUtils.isEmpty(areaNos)) {
            PaymentRuleRoutingQueryDTO routingQuery = PaymentRuleRoutingQueryDTO.builder()
                    .tenantId(query.getTenantId())
                    .businessLine(query.getBusinessLine())
                    .routeKey(PaymentRuleRoutingKeyEnums.AREA_NO.getKey())
                    .routeValue(areaNos.stream().map(String::valueOf).collect(Collectors.toList()))
                    .build();
            List<PaymentRuleRouting> routingList = paymentRuleRoutingDAO.listRuleRouting(routingQuery);
            Set<Long> ruleIds = routingList.stream().map(PaymentRuleRouting::getRuleId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(ruleIds)) {
                return false;
            }
            query.setIds(ruleIds);
        }
        return true;
    }

    private void validatePageListParams(PaymentRuleQueryDTO query) {
        if (query == null) {
            throw new ParamsException("查询条件不能为空");
        }
        if (query.getTenantId() == null) {
            throw new ParamsException("租户ID不能为空");
        }
        if (StringUtils.isEmpty(query.getBusinessLine())) {
            throw new ParamsException("业务线不能为空");
        }
        if (query.getPageIndex() == null || query.getPageSize() == null) {
            throw new ParamsException("分页参数不能为空");
        }
    }

    @Override
    public PaymentRuleDetailDTO queryRuleDetail(Long id) {
        validateQueryRuleDetailParams(id);
        PaymentRule paymentRule = paymentRuleDAO.selectByPrimaryKey(id);
        return paymentRule == null ? null : buildRuleDetailDTO(paymentRule);
    }

    private PaymentRuleDetailDTO buildRuleDetailDTO(PaymentRule paymentRule) {
        PaymentChannel channel = paymentChannelDAO.selectByPrimaryKey(paymentRule.getChannelId());
        List<PaymentRuleRouting> routingList = paymentRuleRoutingDAO.listByRuleIdsAndRouteKeys(Collections.singleton(paymentRule.getId()), Lists.newArrayList(PaymentRuleRoutingKeyEnums.AREA_NO.getKey(), PaymentRuleRoutingKeyEnums.MID.getKey()));
        List<String> areaNos = Lists.newArrayList();
        List<String> mids = Lists.newArrayList();
        if (paymentRule.getTenantId().equals(TenantIdEnums.SUMMER_FARM.getTenantId())) {
            if (!CollectionUtils.isEmpty(routingList)) {
                Map<String, List<String>> groupedRouteValues = routingList.stream()
                        .collect(Collectors.groupingBy(
                                PaymentRuleRouting::getRouteKey,
                                Collectors.mapping(PaymentRuleRouting::getRouteValue, Collectors.toList())
                        ));
                List<String> areaNoList = groupedRouteValues.get(PaymentRuleRoutingKeyEnums.AREA_NO.getKey());
                if (!CollectionUtils.isEmpty(areaNoList)) {
                    areaNos = areaNoList;
                }

                List<String> midList = groupedRouteValues.get(PaymentRuleRoutingKeyEnums.MID.getKey());
                if (!CollectionUtils.isEmpty(midList)) {
                    mids = midList;
                }
            }
        }

        return PaymentRuleDetailDTO.builder()
                .id(paymentRule.getId())
                .sceneName(paymentRule.getSceneName())
                .platform(paymentRule.getPlatform())
                .paymentMethod(paymentRule.getPaymentMethod())
                .channelName(channel.getChannelName())
                .companyEntity(channel.getCompanyEntity())
                .merchantNo(channel.getMerchantNo())
                .subMerchantNo(channel.getSubMerchantNo())
                .areaNos(areaNos.stream().map(Long::valueOf).collect(Collectors.toList()))
                .mids(mids.stream().map(Long::valueOf).collect(Collectors.toList()))
                .channelId(channel.getId())
                .build();
    }

    private void validateQueryRuleDetailParams(Long id) {
        if (id == null) {
            throw new ParamsException("ID不能为空");
        }
    }


    @Transactional
    @Override
    public Long saveRule(PaymentRuleSaveDTO paymentRuleSaveDTO) {
        validateSaveRuleParams(paymentRuleSaveDTO);
        validateUnique(paymentRuleSaveDTO);
        PaymentRule paymentRule = PaymentRuleConvertor.convertToDomain(paymentRuleSaveDTO);
        paymentRuleDAO.insertSelective(paymentRule);
        Long ruleId = paymentRule.getId();
        log.info("生成支付规则，ID：{}", ruleId);
        // 解绑规则路由
        PaymentRuleRoutingBindDTO bindDTO = PaymentRuleRoutingBindDTO.builder()
                .tenantId(paymentRuleSaveDTO.getTenantId())
                .businessLine(paymentRuleSaveDTO.getBusinessLine())
                .areaNos(paymentRuleSaveDTO.getAreaNos())
                .mids(paymentRuleSaveDTO.getMids())
                .paymentMethod(paymentRuleSaveDTO.getPaymentMethod())
                .platform(paymentRuleSaveDTO.getPlatform())
                .build();
        unbindRuleRouting(bindDTO);
        // 绑定规则路由
        bindRuleRouting(ruleId, bindDTO);
        log.info("支付规则保存成功，ID：{}", ruleId);
        return ruleId;
    }

    private void commonUnbindRuleRouting(PaymentRuleRoutingBindDTO bindDTO,
                                         String routeKey,
                                         List<String> routeValues,
                                         String logPrefix) {
        if (CollectionUtils.isEmpty(routeValues)) {
            return;
        }

        PaymentRuleRoutingQueryDTO routingQuery = PaymentRuleRoutingQueryDTO.builder()
                .tenantId(bindDTO.getTenantId())
                .businessLine(bindDTO.getBusinessLine())
                .routeKey(routeKey)
                .routeValue(routeValues.stream().map(String::valueOf).collect(Collectors.toList()))
                .platform(bindDTO.getPlatform())
                .paymentMethod(bindDTO.getPaymentMethod())
                .build();

        List<PaymentRuleRouting> routingList = paymentRuleRoutingDAO.listRuleRouting(routingQuery);
        if (CollectionUtils.isEmpty(routingList)) {
            return;
        }

        List<Long> routingIds = routingList.stream()
                .map(PaymentRuleRouting::getId)
                .collect(Collectors.toList());
        int cnt = paymentRuleRoutingDAO.deleteByIds(routingIds);
        log.info("{}：{}，解绑支付规则路由数量：{}", logPrefix, routeValues, cnt);
    }

    private void unbindRuleRouting(PaymentRuleRoutingBindDTO paymentRuleRoutingBindDTO) {
        // 解绑运营区域路由
        commonUnbindRuleRouting(paymentRuleRoutingBindDTO,
                PaymentRuleRoutingKeyEnums.AREA_NO.getKey(),
                paymentRuleRoutingBindDTO.getAreaNos().stream().map(String::valueOf).collect(Collectors.toList()),
                "运营服务区域");

        // 解绑mid路由
        commonUnbindRuleRouting(paymentRuleRoutingBindDTO,
                PaymentRuleRoutingKeyEnums.MID.getKey(),
                paymentRuleRoutingBindDTO.getMids().stream().map(String::valueOf).collect(Collectors.toList()),
                "门店ids");
    }

    private void unbindRuleRoutingById(Long id) {
        List<PaymentRuleRouting> routingList = paymentRuleRoutingDAO.listByRuleIdsAndRouteKeys(Collections.singleton(id), Lists.newArrayList(PaymentRuleRoutingKeyEnums.AREA_NO.getKey(), PaymentRuleRoutingKeyEnums.MID.getKey()));
        if (!CollectionUtils.isEmpty(routingList)) {
            paymentRuleRoutingDAO.deleteByIds(routingList.stream().map(PaymentRuleRouting::getId).collect(Collectors.toList()));
            log.info("支付规则ID：{}，解绑支付规则路由，运营服务区域:{}", id, routingList.stream().map(PaymentRuleRouting::getRouteValue).collect(Collectors.toList()));
        }
    }

    private void bindRuleRouting(Long ruleId, PaymentRuleRoutingBindDTO paymentRuleRoutingBindDTO) {
        List<PaymentRuleRouting> routingList = buildRuleRouting(ruleId, paymentRuleRoutingBindDTO);
        if (!CollectionUtils.isEmpty(routingList)) {
            log.info("运营服务区域：{}，绑定支付规则路由：{}", paymentRuleRoutingBindDTO.getAreaNos(), ruleId);
            paymentRuleRoutingDAO.batchInsert(routingList);
        }
    }

    private List<PaymentRuleRouting> buildRuleRouting(Long ruleId, PaymentRuleRoutingBindDTO paymentRuleRoutingBindDTO) {
        List<PaymentRuleRouting> routingList = Lists.newArrayList();
        if (paymentRuleRoutingBindDTO.getTenantId().equals(TenantIdEnums.SUMMER_FARM.getTenantId())) {
            List<String> areaNos = paymentRuleRoutingBindDTO.getAreaNos().stream().map(String::valueOf).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(areaNos)) {
                List<PaymentRuleRouting> areaNoRoutingList = buildRoutingForValues(ruleId, paymentRuleRoutingBindDTO, PaymentRuleRoutingKeyEnums.AREA_NO.getKey(), areaNos);
                routingList.addAll(areaNoRoutingList);
            }
            List<String> mids = paymentRuleRoutingBindDTO.getMids().stream().map(String::valueOf).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(mids)) {
                List<PaymentRuleRouting> midRoutingList = buildRoutingForValues(ruleId, paymentRuleRoutingBindDTO, PaymentRuleRoutingKeyEnums.MID.getKey(), mids);
                routingList.addAll(midRoutingList);
            }
        }
        return routingList;
    }

    /**
     * 为指定的值列表构建路由对象
     * @param ruleId 规则ID
     * @param bindDTO 绑定DTO
     * @param routeKey 路由键
     * @param values 值列表
     * @return 路由对象列表
     */
    private <T> List<PaymentRuleRouting> buildRoutingForValues(
            Long ruleId,
            PaymentRuleRoutingBindDTO bindDTO,
            String routeKey,
            List<T> values
    ) {
        return values.stream()
                .map(String::valueOf)
                .map(value -> PaymentRuleRouting.builder()
                        .tenantId(bindDTO.getTenantId())
                        .businessLine(bindDTO.getBusinessLine())
                        .ruleId(ruleId)
                        .routeKey(routeKey)
                        .routeValue(value)
                        .build()
                )
                .collect(Collectors.toList());
    }

    private void validateUnique(PaymentRuleSaveDTO paymentRuleSaveDTO) {
        PaymentRuleQueryDTO query = PaymentRuleQueryDTO.builder()
                .tenantId(paymentRuleSaveDTO.getTenantId())
                .businessLine(paymentRuleSaveDTO.getBusinessLine())
                .platform(paymentRuleSaveDTO.getPlatform())
                .paymentMethod(paymentRuleSaveDTO.getPaymentMethod())
                .channelId(paymentRuleSaveDTO.getChannelId())
                .build();
        List<PaymentRule> paymentRules = paymentRuleDAO.listRule(query);
        if (!CollectionUtils.isEmpty(paymentRules)) {
            throw new BizException("支付规则已存在");
        }
    }

    private void validateSaveRuleParams(PaymentRuleSaveDTO paymentRuleSaveDTO) {
        if (paymentRuleSaveDTO == null) {
            throw new ParamsException("保存参数不能为空");
        }
        if (paymentRuleSaveDTO.getTenantId() == null) {
            throw new ParamsException("租户ID不能为空");
        }
        if (StringUtils.isEmpty(paymentRuleSaveDTO.getBusinessLine())) {
            throw new ParamsException("业务线不能为空");
        }
        if (StringUtils.isEmpty(paymentRuleSaveDTO.getSceneName())) {
            throw new ParamsException("场景名称不能为空");
        }
        if (StringUtils.isEmpty(paymentRuleSaveDTO.getPlatform())) {
            throw new ParamsException("应用平台不能为空");
        }
        if (StringUtils.isEmpty(paymentRuleSaveDTO.getPaymentMethod())) {
            throw new ParamsException("支付方式不能为空");
        }
        if (CollectionUtils.isEmpty(paymentRuleSaveDTO.getAreaNos())) {
            throw new ParamsException("区域不能为空");
        }
        Long channelId = paymentRuleSaveDTO.getChannelId();
        if (channelId == null) {
            throw new ParamsException("支付渠道ID不能为空");
        }
        PaymentChannel paymentChannel = paymentChannelDAO.selectByPrimaryKey(channelId);
        if (paymentChannel == null) {
            throw new BizException("支付渠道不存在");
        }
        if (!PaymentChannelStatusEnums.NORMAL.getStatus().equals(paymentChannel.getStatus())) {
            throw new BizException("非法的支付渠道");
        }
    }

    @Override
    public Boolean bindRuleRouting(PaymentRuleRoutingUpdateDTO paymentRuleRoutingUpdateDTO) {
        validateBindRuleRoutingParams(paymentRuleRoutingUpdateDTO);
        Long id = paymentRuleRoutingUpdateDTO.getId();
        PaymentRule paymentRule = paymentRuleDAO.selectByPrimaryKey(id);
        if (paymentRule == null) {
            throw new BizException("支付规则不存在");
        }

        PaymentRuleRoutingBindDTO bindDTO = PaymentRuleRoutingBindDTO.builder()
                .tenantId(paymentRule.getTenantId())
                .businessLine(paymentRule.getBusinessLine())
                .paymentMethod(paymentRule.getPaymentMethod())
                .platform(paymentRule.getPlatform())
                .build();
        if (!CollectionUtils.isEmpty(paymentRuleRoutingUpdateDTO.getAreaNos()) || !CollectionUtils.isEmpty(paymentRuleRoutingUpdateDTO.getMids())) {
            bindDTO.setAreaNos(paymentRuleRoutingUpdateDTO.getAreaNos());
            bindDTO.setMids(paymentRuleRoutingUpdateDTO.getMids());
            // 根据id解绑 将原路由规则全解绑
            unbindRuleRoutingById(id);
            // 根据运营区域和mid全解绑
            unbindRuleRouting(bindDTO);
            bindRuleRouting(id, bindDTO);
        }
        if (Objects.equals(paymentRuleRoutingUpdateDTO.getRouteKey(), PaymentRuleRoutingKeyEnums.B2B_AUTH_OPEN_ID.getKey())) {
            bindDTO.setOpenIds(paymentRuleRoutingUpdateDTO.getRouteValues());
            bindB2bAuthRouting(id, bindDTO);
        }
        return Boolean.TRUE;
    }

    private void bindB2bAuthRouting(Long ruleId, PaymentRuleRoutingBindDTO bindDTO) {
        List<String> openIds = bindDTO.getOpenIds();
        PaymentRuleRoutingQueryDTO query = PaymentRuleRoutingQueryDTO.builder()
                .tenantId(bindDTO.getTenantId())
                .businessLine(bindDTO.getBusinessLine())
                .routeKey(PaymentRuleRoutingKeyEnums.B2B_AUTH_OPEN_ID.getKey())
                .routeValue(openIds)
                .build();
        List<PaymentRuleRouting> routingList = paymentRuleRoutingDAO.listRuleRouting(query);
        // 如果有绑定的记录
        if (!CollectionUtils.isEmpty(routingList)) {
            log.info("B2B授权记录已绑定：{}, ruleId:{}", openIds, ruleId);
            return;
        }
        List<PaymentRuleRouting> routingInsertList = openIds.stream().map(item -> {
            return PaymentRuleRouting.builder()
                    .tenantId(bindDTO.getTenantId())
                    .businessLine(bindDTO.getBusinessLine())
                    .routeKey(PaymentRuleRoutingKeyEnums.B2B_AUTH_OPEN_ID.getKey())
                    .routeValue(item)
                    .ruleId(ruleId)
                    .build();
        }).collect(Collectors.toList());
        paymentRuleRoutingDAO.batchInsert(routingInsertList);
        log.info("B2B授权记录绑定成功：{}, ruleId:{}", openIds, ruleId);
    }

    private void validateBindRuleRoutingParams(PaymentRuleRoutingUpdateDTO paymentRuleRoutingUpdateDTO) {
        if (paymentRuleRoutingUpdateDTO == null) {
            throw new ParamsException("参数不能为空");
        }
        if (paymentRuleRoutingUpdateDTO.getId() == null) {
            throw new ParamsException("支付规则ID不能为空");
        }
    }

    @Override
    public PaymentRuleDTO selectEarliestRule(PaymentRoutingQueryDTO paymentRoutingQueryDTO) {
        validateSelectEarliestRuleParams(paymentRoutingQueryDTO);
        PaymentRoutingQueryDTO query = PaymentRoutingQueryDTO.builder()
                .tenantId(paymentRoutingQueryDTO.getTenantId())
                .businessLine(paymentRoutingQueryDTO.getBusinessLine())
                .platform(paymentRoutingQueryDTO.getPlatform())
                .paymentMethod(paymentRoutingQueryDTO.getPaymentMethod())
                .channelName(paymentRoutingQueryDTO.getChannelName())
                .status(paymentRoutingQueryDTO.getStatus())
                .build();
        PaymentRule paymentRule = paymentRuleDAO.selectEarliestRule(query);
        return paymentRule == null ? null : buildRuleDTO(paymentRule);
    }

    private PaymentRuleDTO buildRuleDTO(PaymentRule paymentRule) {
        return PaymentRuleDTO.builder()
                .id(paymentRule.getId())
                .sceneName(paymentRule.getSceneName())
                .platform(paymentRule.getPlatform())
                .paymentMethod(paymentRule.getPaymentMethod())
                .channelId(paymentRule.getChannelId())
                .build();
    }

    private void validateSelectEarliestRuleParams(PaymentRoutingQueryDTO paymentRoutingQueryDTO) {
        if (paymentRoutingQueryDTO == null) {
            throw new ParamsException("查询参数不能为空");
        }
        if (paymentRoutingQueryDTO.getTenantId() == null) {
            throw new ParamsException("租户ID不能为空");
        }
        if (StringUtils.isEmpty(paymentRoutingQueryDTO.getBusinessLine())) {
            throw new ParamsException("业务线不能为空");
        }
    }
}
