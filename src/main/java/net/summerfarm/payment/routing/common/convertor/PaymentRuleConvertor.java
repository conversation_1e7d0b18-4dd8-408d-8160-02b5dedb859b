package net.summerfarm.payment.routing.common.convertor;

import net.summerfarm.payment.routing.model.domain.PaymentRule;
import net.summerfarm.payment.routing.model.dto.PaymentRuleListDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRuleSaveDTO;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 支付规则转换器
 * @author: George
 * @date: 2024-11-29
 **/
public class PaymentRuleConvertor {
    public static PaymentRule convertToDomain(PaymentRuleSaveDTO paymentRuleSaveDTO) {
        if (paymentRuleSaveDTO == null) {
            return null;
        }
        PaymentRule paymentRule = new PaymentRule();
        paymentRule.setTenantId(paymentRuleSaveDTO.getTenantId());
        paymentRule.setBusinessLine(paymentRuleSaveDTO.getBusinessLine());
        paymentRule.setSceneName(paymentRuleSaveDTO.getSceneName());
        paymentRule.setPlatform(paymentRuleSaveDTO.getPlatform());
        paymentRule.setPaymentMethod(paymentRuleSaveDTO.getPaymentMethod());
        paymentRule.setChannelId(paymentRuleSaveDTO.getChannelId());
        return paymentRule;
    }

    public static List<PaymentRuleListDTO> convert2ListDTO(List<PaymentRule> paymentRules) {
        if (CollectionUtils.isEmpty(paymentRules)) {
            return Collections.emptyList();
        }
        return paymentRules.stream().map(item -> {
            PaymentRuleListDTO paymentRuleListDTO = new PaymentRuleListDTO();
            paymentRuleListDTO.setId(item.getId());
            paymentRuleListDTO.setChannelId(item.getChannelId());
            paymentRuleListDTO.setPaymentMethod(item.getPaymentMethod());
            paymentRuleListDTO.setPlatform(item.getPlatform());
            paymentRuleListDTO.setSceneName(item.getSceneName());
            return paymentRuleListDTO;
        }).collect(Collectors.toList());
    }
}
