package net.summerfarm.payment.routing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2024-12-11
 **/
@Getter
@AllArgsConstructor
public enum PaymentChannelTypeEnums {

    WECHAT_NATIVE("WECHAT_NATIVE", "微信原生支付"),
    CMB_WECHAT("CMB_WECHAT", "招行微信支付"),
    CMB_ALIPAY("CMB_ALIPAY", "招行支付宝支付"),
    CMB_CODE("CMB_CODE", "招行扫码支付"),
    XIANMU_CARD("XIANMU_CARD", "鲜沐卡支付"),
    BILL("BILL", "账期支付"),
    FIRE_FACE_WECHAT("FIRE_FACE_WECHAT", "火脸微信支付"),
    DIN_WECHAT_MP("DIN_WECHAT_MP", "智付小程序微信支付"),
    DIN_WECHAT_H5("DIN_WECHAT_H5", "智付H5微信支付"),
    ;

    private String type;

    private String desc;

    public static String getChannelTypeByMethodAndCode(Integer method, Integer providerCode, boolean isMiniApp) {
        if (Objects.equals(method, PaymentMethodEnums.WECHAT.getMethod()) && Objects.equals(providerCode, PaymentChannelProviderEnums.WECHAT_NATIVE.getCode())) {
            return WECHAT_NATIVE.getType();
        }
        if (Objects.equals(method, PaymentMethodEnums.WECHAT.getMethod()) && Objects.equals(providerCode, PaymentChannelProviderEnums.CMB.getCode())) {
            return CMB_WECHAT.getType();
        }
        if (Objects.equals(method, PaymentMethodEnums.WECHAT.getMethod()) && Objects.equals(providerCode, PaymentChannelProviderEnums.WECHAT_B2B.getCode())) {
            return FIRE_FACE_WECHAT.getType();
        }
        if (Objects.equals(method, PaymentMethodEnums.ALIPAY.getMethod()) && Objects.equals(providerCode, PaymentChannelProviderEnums.CMB.getCode())) {
            return CMB_ALIPAY.getType();
        }
        if (Objects.equals(method, PaymentMethodEnums.PAYMENT_CODE.getMethod()) && Objects.equals(providerCode, PaymentChannelProviderEnums.CMB.getCode())) {
            return CMB_CODE.getType();
        }
        if (Objects.equals(method, PaymentMethodEnums.XIANMU_CARD.getMethod())) {
            return XIANMU_CARD.getType();
        }
        if (Objects.equals(method, PaymentMethodEnums.BILL.getMethod())) {
            return BILL.getType();
        }
        if (Objects.equals(method, PaymentMethodEnums.WECHAT.getMethod()) && Objects.equals(providerCode, PaymentChannelProviderEnums.DIN_PAY.getCode()) && isMiniApp) {
            return DIN_WECHAT_MP.getType();
        }
        if (Objects.equals(method, PaymentMethodEnums.WECHAT.getMethod()) && Objects.equals(providerCode, PaymentChannelProviderEnums.DIN_PAY.getCode()) && !isMiniApp) {
            return DIN_WECHAT_H5.getType();
        }

        return null;
    }
}
