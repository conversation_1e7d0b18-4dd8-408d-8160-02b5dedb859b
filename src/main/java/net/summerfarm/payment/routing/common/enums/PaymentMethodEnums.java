package net.summerfarm.payment.routing.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @description: 支付方式枚举类
 * @author: <PERSON>
 * @date: 2024-12-03
 **/
@Getter
@AllArgsConstructor
public enum PaymentMethodEnums {

    WECHAT(0, "wechat"),
    XIANMU_CARD(1, "xianmu-card"),
    PAYMENT_CODE(2, "payment-code"),
    BILL(3, "bill"),
    ALIPAY(4, "alipay"),
    ;

    /**
     * 支付方式
     */
    private Integer method;

    /**
     * 支付方式编码
     */
    private String code;

    public static String getCodeByMethod(Integer method) {
        for (PaymentMethodEnums methodEnum : PaymentMethodEnums.values()) {
            if (methodEnum.getMethod().equals(method)) {
                return methodEnum.getCode();
            }
        }
        return null;
    }

    /**
     * 是否是本地支付方式
     * @param paymentMethod
     * @return
     */
    public static boolean isNativePaymentMethods(Integer paymentMethod) {
        return Objects.equals(paymentMethod, PaymentMethodEnums.XIANMU_CARD.getMethod())
                || Objects.equals(paymentMethod, PaymentMethodEnums.BILL.getMethod());
    }
}
