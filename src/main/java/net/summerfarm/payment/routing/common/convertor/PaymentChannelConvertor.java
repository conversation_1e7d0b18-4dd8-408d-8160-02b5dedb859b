package net.summerfarm.payment.routing.common.convertor;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import net.summerfarm.payment.routing.model.domain.PaymentChannel;
import net.summerfarm.payment.routing.model.dto.PaymentChannelDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelDetailDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelListDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelTiledDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 转换器类
 * @author: George
 * @date: 2024-11-28
 **/
public class PaymentChannelConvertor {

    public static List<PaymentChannelListDTO> convert2ListDTO(List<PaymentChannel> paymentChannels) {
        return paymentChannels.stream().map(PaymentChannelConvertor::convert2ListDTO).collect(Collectors.toList());
    }

    public static PaymentChannelListDTO convert2ListDTO(PaymentChannel paymentChannel) {
        if (paymentChannel == null) {
            return null;
        }
        PaymentChannelListDTO paymentChannelListDTO = new PaymentChannelListDTO();
        paymentChannelListDTO.setId(paymentChannel.getId());
        paymentChannelListDTO.setChannelName(paymentChannel.getChannelName());
        paymentChannelListDTO.setCompanyEntity(paymentChannel.getCompanyEntity());
        paymentChannelListDTO.setMerchantNo(paymentChannel.getMerchantNo());
        paymentChannelListDTO.setOperatorAdminId(paymentChannel.getOperatorAdminId());
        paymentChannelListDTO.setOperateTime(paymentChannel.getOperateTime());
        paymentChannelListDTO.setStatus(paymentChannel.getStatus());
        return paymentChannelListDTO;
    }

    public static PaymentChannelDetailDTO convert2DetailDTO(PaymentChannel channel) {
        if (channel == null) {
            return null;
        }
        PaymentChannelDetailDTO paymentChannelDetailDTO = new PaymentChannelDetailDTO();
        paymentChannelDetailDTO.setId(channel.getId());
        paymentChannelDetailDTO.setChannelName(channel.getChannelName());
        paymentChannelDetailDTO.setCompanyEntity(channel.getCompanyEntity());
        paymentChannelDetailDTO.setMerchantNo(channel.getMerchantNo());
        paymentChannelDetailDTO.setSubMerchantNo(channel.getSubMerchantNo());
        return paymentChannelDetailDTO;
    }

    public static PaymentChannelDTO convert2DTO(PaymentChannel paymentChannel) {
        if (paymentChannel == null) {
            return null;
        }
        PaymentChannelDTO paymentChannelDTO = new PaymentChannelDTO();
        paymentChannelDTO.setId(paymentChannel.getId());
        paymentChannelDTO.setTenantId(paymentChannel.getTenantId());
        paymentChannelDTO.setBusinessLine(paymentChannel.getBusinessLine());
        paymentChannelDTO.setChannelName(paymentChannel.getChannelName());
        paymentChannelDTO.setCompanyEntity(paymentChannel.getCompanyEntity());
        paymentChannelDTO.setOperatorAdminId(paymentChannel.getOperatorAdminId());
        paymentChannelDTO.setOperateTime(paymentChannel.getOperateTime());
        paymentChannelDTO.setStatus(paymentChannel.getStatus());
        paymentChannelDTO.setMerchantNo(paymentChannel.getMerchantNo());
        paymentChannelDTO.setSubMerchantNo(paymentChannel.getSubMerchantNo());
        paymentChannelDTO.setPublicKey(paymentChannel.getPublicKey());
        paymentChannelDTO.setPrivateKey(paymentChannel.getPrivateKey());
        paymentChannelDTO.setSecret(paymentChannel.getSecret());
        paymentChannelDTO.setExtraConfig(paymentChannel.getExtraConfig());
        return paymentChannelDTO;
    }

    public static PaymentChannelTiledDTO convert2TiledDTO(PaymentChannel paymentChannel) {

        if (paymentChannel == null) {
            return null;
        }
        String extraConfig = paymentChannel.getExtraConfig();
        PaymentChannelTiledDTO paymentChannelTiledDTO = JSON.parseObject(extraConfig, PaymentChannelTiledDTO.class);
        paymentChannelTiledDTO.setId(paymentChannel.getId());
        paymentChannelTiledDTO.setTenantId(paymentChannel.getTenantId());
        paymentChannelTiledDTO.setBusinessLine(paymentChannel.getBusinessLine());
        paymentChannelTiledDTO.setChannelName(paymentChannel.getChannelName());
        paymentChannelTiledDTO.setCompanyEntity(paymentChannel.getCompanyEntity());
        paymentChannelTiledDTO.setMerchantNo(paymentChannel.getMerchantNo());
        paymentChannelTiledDTO.setPublicKey(paymentChannel.getPublicKey());
        paymentChannelTiledDTO.setPrivateKey(paymentChannel.getPrivateKey());
        paymentChannelTiledDTO.setSecret(paymentChannel.getSecret());
        paymentChannelTiledDTO.setCompanyAccountId(paymentChannel.getCompanyAccountId());
        return paymentChannelTiledDTO;
    }
}
