package net.summerfarm.payment.routing.common.convertor;

import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.function.Function;

/**
 * @description:
 * @author: <PERSON>
 * @date: 2025-02-21
 **/
public class PageInfoConvertor {

    public static <T, R> PageInfo<R> convert2PageInfoDTO(PageInfo<T> pageInfo, Function<List<T>, List<R>> converter) {
        PageInfo<R> page = new PageInfo<>();
        page.setEndRow(pageInfo.getEndRow());
        page.setFirstPage(pageInfo.getFirstPage());
        page.setHasNextPage(pageInfo.isHasNextPage());
        page.setHasPreviousPage(pageInfo.isHasPreviousPage());
        page.setIsFirstPage(pageInfo.isIsFirstPage());
        page.setIsLastPage(pageInfo.isIsLastPage());
        page.setLastPage(pageInfo.getLastPage());
        page.setList(converter.apply(pageInfo.getList()));
        page.setNavigatePages(pageInfo.getNavigatePages());
        page.setNavigatepageNums(pageInfo.getNavigatepageNums());
        page.setNextPage(pageInfo.getNextPage());
        page.setPageNum(pageInfo.getPageNum());
        page.setPageSize(pageInfo.getPageSize());
        page.setPages(pageInfo.getPages());
        page.setPrePage(pageInfo.getPrePage());
        page.setSize(pageInfo.getSize());
        page.setStartRow(pageInfo.getStartRow());
        page.setTotal(pageInfo.getTotal());
        return page;
    }
}
