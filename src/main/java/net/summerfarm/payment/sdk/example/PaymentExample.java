package net.summerfarm.payment.sdk.example;

import net.summerfarm.payment.sdk.model.ChannelConfig;
import net.summerfarm.payment.sdk.model.UnifiedPaymentRequest;
import net.summerfarm.payment.sdk.model.UnifiedPaymentResult;
import net.summerfarm.payment.sdk.service.PaymentClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 统一支付SDK使用示例
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Component
public class PaymentExample {

    @Autowired
    private PaymentClientService paymentClientService;

    /**
     * 智付微信小程序支付示例
     */
    public void zhifuWechatMiniAppPaymentExample() {
        // 1. 构建渠道配置
        ChannelConfig channelConfig = ChannelConfig.builder()
                .channelCode("zhifu")
                .channelName("智付间联")
                .merchantNo("your_merchant_no")
                .privateKey("your_private_key")
                .publicKey("your_public_key")
                .secretKey("your_secret_key")
                .sandbox(false)
                .connectTimeout(30000)
                .readTimeout(60000)
                .build();

        // 2. 构建支付请求
        UnifiedPaymentRequest request = UnifiedPaymentRequest.builder()
                .paymentNo("ORDER_" + System.currentTimeMillis())
                .amount(new BigDecimal("99.99"))
                .currency("CNY")
                .subject("测试商品")
                .body("这是一个测试订单")
                .tenantId(1001L) // SaaS租户ID
                .businessLine("saas")
                .paymentMethod("wechat")
                .platform("miniapp")
                .channelConfig(channelConfig)
                .userId("user_openid_123")
                .userIp("*************")
                .notifyUrl("https://your-domain.com/payment/notify")
                .expireMinutes(30)
                .needProfitSharing(false)
                .source("saas_mall")
                .build();

        // 3. 发起支付
        UnifiedPaymentResult result = paymentClientService.pay(request);

        // 4. 处理结果
        if (result.isSuccess()) {
            System.out.println("支付成功: " + result.getChannelTransactionId());
        } else if (result.isPending()) {
            System.out.println("支付处理中，二维码: " + result.getCredential().getContent());
        } else {
            System.out.println("支付失败: " + result.getError().getMessage());
        }
    }

    /**
     * 鲜沐商城微信原生支付示例
     */
    public void summerfarmWechatNativePaymentExample() {
        // 1. 构建渠道配置
        ChannelConfig channelConfig = ChannelConfig.builder()
                .channelCode("wechat_native")
                .channelName("微信原生")
                .merchantNo("your_wechat_merchant_no")
                .appId("your_wechat_app_id")
                .secretKey("your_wechat_key")
                .certificate("your_cert_path")
                .sandbox(false)
                .build();

        // 2. 构建支付请求
        UnifiedPaymentRequest request = UnifiedPaymentRequest.builder()
                .paymentNo("SF_ORDER_" + System.currentTimeMillis())
                .amount(new BigDecimal("158.80"))
                .subject("新鲜蔬菜套餐")
                .body("包含时令蔬菜5种")
                .tenantId(1L) // 鲜沐商城租户ID
                .businessLine("summerfarm")
                .paymentMethod("wechat")
                .platform("miniapp")
                .channelConfig(channelConfig)
                .userId("wx_openid_456")
                .storeId(1001L)
                .areaNo(110100L)
                .notifyUrl("https://summerfarm.com/payment/notify")
                .expireMinutes(15)
                .build();

        // 3. 发起支付
        UnifiedPaymentResult result = paymentClientService.pay(request);

        // 4. 处理结果
        handlePaymentResult(result);
    }

    /**
     * 处理支付结果的通用方法
     */
    private void handlePaymentResult(UnifiedPaymentResult result) {
        System.out.println("=== 支付结果 ===");
        System.out.println("订单号: " + result.getPaymentNo());
        System.out.println("状态: " + result.getStatus());
        System.out.println("渠道: " + result.getChannelName());
        System.out.println("追踪ID: " + result.getTraceId());

        switch (result.getStatus()) {
            case SUCCESS:
                System.out.println("支付成功!");
                System.out.println("渠道交易号: " + result.getChannelTransactionId());
                System.out.println("支付时间: " + result.getPaymentTime());
                break;

            case PENDING:
                System.out.println("支付处理中...");
                if (result.getCredential() != null) {
                    System.out.println("凭证类型: " + result.getCredential().getType());
                    System.out.println("凭证内容: " + result.getCredential().getContent());
                }
                break;

            case FAILED:
                System.out.println("支付失败!");
                if (result.getError() != null) {
                    System.out.println("错误码: " + result.getError().getCode());
                    System.out.println("错误信息: " + result.getError().getMessage());
                    if (result.getError().getChannelErrorCode() != null) {
                        System.out.println("渠道错误码: " + result.getError().getChannelErrorCode());
                        System.out.println("渠道错误信息: " + result.getError().getChannelErrorMessage());
                    }
                }
                break;

            default:
                System.out.println("未知状态: " + result.getStatus());
                break;
        }

        if (result.getRawResponse() != null) {
            System.out.println("原始响应: " + result.getRawResponse());
        }
    }
}
