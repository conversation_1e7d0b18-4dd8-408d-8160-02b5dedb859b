package net.summerfarm.payment.sdk.adapter.impl;

import net.summerfarm.payment.sdk.adapter.ChannelAdapter;
import net.summerfarm.payment.sdk.common.enums.ErrorCode;
import net.summerfarm.payment.sdk.common.enums.FeatureType;
import net.summerfarm.payment.sdk.common.enums.PaymentStatus;
import net.summerfarm.payment.sdk.common.exception.ChannelException;
import net.summerfarm.payment.sdk.model.ChannelConfig;
import net.summerfarm.payment.sdk.model.UnifiedPaymentRequest;
import net.summerfarm.payment.sdk.model.UnifiedPaymentResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.EnumSet;
import java.util.Set;
import java.util.UUID;

/**
 * 智付渠道适配器示例实现
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Component
public class ZhifuChannelAdapter implements ChannelAdapter {

    private static final Logger log = LoggerFactory.getLogger(ZhifuChannelAdapter.class);

    private static final String CHANNEL_CODE = "zhifu";
    private static final String CHANNEL_NAME = "智付间联";

    @Override
    public String getChannelCode() {
        return CHANNEL_CODE;
    }

    @Override
    public String getChannelName() {
        return CHANNEL_NAME;
    }

    @Override
    public Set<FeatureType> getSupportedFeatures() {
        return EnumSet.of(
                FeatureType.H5_PAYMENT_SUPPORT,
                FeatureType.MINI_APP_PAYMENT_SUPPORT,
                FeatureType.QR_CODE_PAYMENT_SUPPORT,
                FeatureType.REFUND_SUPPORT,
                FeatureType.PARTIAL_REFUND_SUPPORT,
                FeatureType.ORDER_QUERY_SUPPORT,
                FeatureType.ORDER_CLOSE_SUPPORT,
                FeatureType.ASYNC_NOTIFY_SUPPORT,
                FeatureType.ALLOW_ONE_CENT_ORDER
                // 注意：智付暂不支持分账功能
        );
    }

    @Override
    public UnifiedPaymentResult doPay(UnifiedPaymentRequest request) {
        log.info("智付渠道开始处理支付: paymentNo={}, amount={}", 
                request.getPaymentNo(), request.getAmount());

        try {
            // 1. 验证请求参数
            validateRequest(request);
            validateZhifuSpecificParams(request);

            // 2. 构建智付支付请求
            ZhifuPaymentRequest zhifuRequest = buildZhifuRequest(request);

            // 3. 调用智付API（这里是示例，实际需要调用真实API）
            ZhifuPaymentResponse zhifuResponse = callZhifuPaymentApi(zhifuRequest);

            // 4. 转换响应结果
            return convertToUnifiedResult(request, zhifuResponse);

        } catch (ChannelException e) {
            log.error("智付支付失败: paymentNo={}, channelError={}", 
                    request.getPaymentNo(), e.getChannelErrorMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("智付支付异常: paymentNo={}", request.getPaymentNo(), e);
            throw new ChannelException(ErrorCode.CHANNEL_UNAVAILABLE, CHANNEL_NAME, 
                    "SYSTEM_ERROR", e.getMessage(), e);
        }
    }

    /**
     * 验证智付特定参数
     */
    private void validateZhifuSpecificParams(UnifiedPaymentRequest request) {
        ChannelConfig config = request.getChannelConfig();
        
        if (config.getMerchantNo() == null || config.getMerchantNo().trim().isEmpty()) {
            throw new ChannelException(ErrorCode.CHANNEL_CONFIG_ERROR, CHANNEL_NAME, 
                    "智付商户号不能为空");
        }
        
        if (config.getPrivateKey() == null || config.getPrivateKey().trim().isEmpty()) {
            throw new ChannelException(ErrorCode.CHANNEL_CONFIG_ERROR, CHANNEL_NAME, 
                    "智付私钥不能为空");
        }
        
        if (config.getPublicKey() == null || config.getPublicKey().trim().isEmpty()) {
            throw new ChannelException(ErrorCode.CHANNEL_CONFIG_ERROR, CHANNEL_NAME, 
                    "智付公钥不能为空");
        }
    }

    /**
     * 构建智付支付请求
     */
    private ZhifuPaymentRequest buildZhifuRequest(UnifiedPaymentRequest request) {
        ChannelConfig config = request.getChannelConfig();
        
        return ZhifuPaymentRequest.builder()
                .merchantNo(config.getMerchantNo())
                .orderNo(request.getPaymentNo())
                .amount(request.getAmount().multiply(new java.math.BigDecimal("100")).intValue()) // 转换为分
                .subject(request.getSubject())
                .body(request.getBody())
                .notifyUrl(request.getNotifyUrl())
                .returnUrl(request.getReturnUrl())
                .paymentMethod(mapPaymentMethod(request.getPaymentMethod(), request.getPlatform()))
                .userIp(request.getUserIp())
                .build();
    }

    /**
     * 映射支付方式
     */
    private String mapPaymentMethod(String paymentMethod, String platform) {
        // 根据统一支付方式和平台映射为智付的支付方式
        if ("wechat".equals(paymentMethod)) {
            if ("miniapp".equals(platform)) {
                return "WECHAT_MP";
            } else if ("h5".equals(platform)) {
                return "WECHAT_H5";
            }
        } else if ("alipay".equals(paymentMethod)) {
            return "ALIPAY";
        }
        
        throw new ChannelException(ErrorCode.CHANNEL_FEATURE_NOT_SUPPORTED, CHANNEL_NAME, 
                "不支持的支付方式: " + paymentMethod + "@" + platform);
    }

    /**
     * 调用智付支付API（示例实现）
     */
    private ZhifuPaymentResponse callZhifuPaymentApi(ZhifuPaymentRequest request) {
        // TODO: 实际调用智付API
        // 这里是示例实现，返回模拟数据
        
        log.info("调用智付支付API: merchantNo={}, orderNo={}, amount={}", 
                request.getMerchantNo(), request.getOrderNo(), request.getAmount());
        
        // 模拟API调用
        return ZhifuPaymentResponse.builder()
                .code("SUCCESS")
                .message("支付成功")
                .orderNo(request.getOrderNo())
                .zhifuOrderNo("ZF" + System.currentTimeMillis())
                .paymentUrl("https://pay.zhifu.com/pay?token=" + UUID.randomUUID().toString())
                .qrCode("weixin://wxpay/bizpayurl?pr=" + UUID.randomUUID().toString())
                .build();
    }

    /**
     * 转换为统一结果
     */
    private UnifiedPaymentResult convertToUnifiedResult(UnifiedPaymentRequest request, 
                                                       ZhifuPaymentResponse response) {
        if ("SUCCESS".equals(response.getCode())) {
            // 构建支付凭证
            UnifiedPaymentResult.PaymentCredential credential = 
                    UnifiedPaymentResult.PaymentCredential.builder()
                            .type("QR_CODE")
                            .content(response.getQrCode())
                            .build();
            
            return UnifiedPaymentResult.pending(
                    request.getPaymentNo(),
                    response.getZhifuOrderNo(),
                    credential,
                    CHANNEL_NAME
            );
        } else {
            throw new ChannelException(ErrorCode.CHANNEL_UNAVAILABLE, CHANNEL_NAME, 
                    response.getCode(), response.getMessage());
        }
    }

    // 智付请求模型（内部使用）
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    private static class ZhifuPaymentRequest {
        private String merchantNo;
        private String orderNo;
        private Integer amount;
        private String subject;
        private String body;
        private String paymentMethod;
        private String notifyUrl;
        private String returnUrl;
        private String userIp;
    }

    // 智付响应模型（内部使用）
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    private static class ZhifuPaymentResponse {
        private String code;
        private String message;
        private String orderNo;
        private String zhifuOrderNo;
        private String paymentUrl;
        private String qrCode;
    }
}
