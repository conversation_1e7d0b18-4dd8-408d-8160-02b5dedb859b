package net.summerfarm.payment.sdk.common.exception;

import net.summerfarm.payment.sdk.common.enums.ErrorCode;

/**
 * 签名验证异常
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
public class SignatureException extends PaymentException {

    public SignatureException(ErrorCode errorCode) {
        super(errorCode);
    }

    public SignatureException(ErrorCode errorCode, String detailMessage) {
        super(errorCode, detailMessage);
    }

    public SignatureException(ErrorCode errorCode, String detailMessage, Throwable cause) {
        super(errorCode, detailMessage, cause);
    }

    public SignatureException(ErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }
}
