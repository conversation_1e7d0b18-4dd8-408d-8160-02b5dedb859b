package net.summerfarm.payment.sdk.common.exception;

import net.summerfarm.payment.sdk.common.enums.ErrorCode;

/**
 * 渠道相关异常
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
public class ChannelException extends PaymentException {

    /**
     * 渠道名称
     */
    private final String channelName;

    /**
     * 渠道返回的原始错误码
     */
    private final String channelErrorCode;

    /**
     * 渠道返回的原始错误信息
     */
    private final String channelErrorMessage;

    public ChannelException(ErrorCode errorCode, String channelName) {
        super(errorCode);
        this.channelName = channelName;
        this.channelErrorCode = null;
        this.channelErrorMessage = null;
    }

    public ChannelException(ErrorCode errorCode, String channelName, String detailMessage) {
        super(errorCode, detailMessage);
        this.channelName = channelName;
        this.channelErrorCode = null;
        this.channelErrorMessage = null;
    }

    public ChannelException(ErrorCode errorCode, String channelName, String channelErrorCode, String channelErrorMessage) {
        super(errorCode, String.format("渠道[%s]错误: %s - %s", channelName, channelErrorCode, channelErrorMessage));
        this.channelName = channelName;
        this.channelErrorCode = channelErrorCode;
        this.channelErrorMessage = channelErrorMessage;
    }

    public ChannelException(ErrorCode errorCode, String channelName, String channelErrorCode, String channelErrorMessage, Throwable cause) {
        super(errorCode, String.format("渠道[%s]错误: %s - %s", channelName, channelErrorCode, channelErrorMessage), cause);
        this.channelName = channelName;
        this.channelErrorCode = channelErrorCode;
        this.channelErrorMessage = channelErrorMessage;
    }

    public String getChannelName() {
        return channelName;
    }

    public String getChannelErrorCode() {
        return channelErrorCode;
    }

    public String getChannelErrorMessage() {
        return channelErrorMessage;
    }
}
