package net.summerfarm.payment.sdk.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统一错误码枚举
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Getter
@AllArgsConstructor
public enum ErrorCode {

    // 通用错误 1000-1999
    SUCCESS("1000", "成功"),
    SYSTEM_ERROR("1001", "系统错误"),
    INVALID_PARAMETER("1002", "参数错误"),
    NETWORK_ERROR("1003", "网络错误"),
    TIMEOUT_ERROR("1004", "请求超时"),

    // 业务错误 2000-2999
    PAYMENT_ORDER_NOT_FOUND("2001", "支付订单不存在"),
    PAYMENT_ORDER_STATUS_ERROR("2002", "支付订单状态错误"),
    DUPLICATE_PAYMENT_ORDER("2003", "重复的支付订单"),
    PAYMENT_AMOUNT_ERROR("2004", "支付金额错误"),
    PAYMENT_EXPIRED("2005", "支付订单已过期"),

    // 渠道相关错误 3000-3999
    CHANNEL_NOT_SUPPORTED("3001", "不支持的支付渠道"),
    CHANNEL_CONFIG_ERROR("3002", "渠道配置错误"),
    CHANNEL_UNAVAILABLE("3003", "支付渠道不可用"),
    CHANNEL_FEATURE_NOT_SUPPORTED("3004", "渠道不支持此功能"),

    // 签名验证错误 4000-4999
    SIGNATURE_ERROR("4001", "签名验证失败"),
    SIGNATURE_MISSING("4002", "缺少签名"),
    CERTIFICATE_ERROR("4003", "证书错误"),

    // 租户相关错误 5000-5999
    TENANT_NOT_FOUND("5001", "租户不存在"),
    TENANT_DISABLED("5002", "租户已禁用"),
    TENANT_CONFIG_ERROR("5003", "租户配置错误"),

    // 路由相关错误 6000-6999
    ROUTING_NOT_FOUND("6001", "未找到支付路由"),
    ROUTING_CONFIG_ERROR("6002", "路由配置错误"),

    // 未知错误
    UNKNOWN_ERROR("9999", "未知错误");

    /**
     * 错误码
     */
    private final String code;

    /**
     * 错误描述
     */
    private final String message;

    /**
     * 根据错误码获取枚举
     * 
     * @param code 错误码
     * @return ErrorCode
     */
    public static ErrorCode fromCode(String code) {
        for (ErrorCode errorCode : ErrorCode.values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode;
            }
        }
        return UNKNOWN_ERROR;
    }

    /**
     * 是否为成功状态
     * 
     * @return boolean
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }
}
