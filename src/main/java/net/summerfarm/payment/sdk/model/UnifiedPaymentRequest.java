package net.summerfarm.payment.sdk.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一支付请求模型
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedPaymentRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务方唯一订单号（必填）
     */
    private String paymentNo;

    /**
     * 交易金额（必填，单位：元）
     */
    private BigDecimal amount;

    /**
     * 币种（可选，默认CNY）
     */
    private String currency;

    /**
     * 订单标题/商品描述（必填）
     */
    private String subject;

    /**
     * 订单详细描述（可选）
     */
    private String body;

    /**
     * 租户ID（SaaS场景必填）
     */
    private Long tenantId;

    /**
     * 业务线标识（必填）
     */
    private String businessLine;

    /**
     * 支付方式（必填）
     * @see net.summerfarm.payment.routing.common.enums.PaymentMethodEnums
     */
    private String paymentMethod;

    /**
     * 应用平台（必填：miniapp, h5等）
     */
    private String platform;

    /**
     * 渠道配置信息（必填）
     */
    private ChannelConfig channelConfig;

    /**
     * 用户标识（可选，如微信openId、支付宝userId等）
     */
    private String userId;

    /**
     * 用户IP地址（可选，部分渠道需要）
     */
    private String userIp;

    /**
     * 异步通知地址（可选）
     */
    private String notifyUrl;

    /**
     * 同步返回地址（可选，主要用于H5支付）
     */
    private String returnUrl;

    /**
     * 订单过期时间（可选）
     */
    private LocalDateTime expireTime;

    /**
     * 订单过期时长（分钟，可选，与expireTime二选一）
     */
    private Integer expireMinutes;

    /**
     * 门店ID（可选，用于路由）
     */
    private Long storeId;

    /**
     * 区域编号（可选，用于路由）
     */
    private Long areaNo;

    /**
     * 销售主体名称（可选）
     */
    private String sellingEntityName;

    /**
     * 是否需要分账（可选，默认false）
     */
    private Boolean needProfitSharing;

    /**
     * 分账信息（可选，当needProfitSharing为true时使用）
     */
    private ProfitSharingInfo profitSharingInfo;

    /**
     * 扩展参数（可选）
     */
    private Map<String, Object> extraParams;

    /**
     * 请求来源标识（可选，用于日志追踪）
     */
    private String source;

    /**
     * 请求追踪ID（可选，用于链路追踪）
     */
    private String traceId;

    /**
     * 获取扩展参数值
     * 
     * @param key 参数键
     * @param defaultValue 默认值
     * @param <T> 值类型
     * @return 参数值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtraParam(String key, T defaultValue) {
        if (extraParams == null || !extraParams.containsKey(key)) {
            return defaultValue;
        }
        try {
            return (T) extraParams.get(key);
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }

    /**
     * 获取扩展参数值
     * 
     * @param key 参数键
     * @param <T> 值类型
     * @return 参数值，可能为null
     */
    public <T> T getExtraParam(String key) {
        return getExtraParam(key, null);
    }

    /**
     * 设置扩展参数值
     * 
     * @param key 参数键
     * @param value 参数值
     */
    public void setExtraParam(String key, Object value) {
        if (extraParams == null) {
            extraParams = new java.util.HashMap<>();
        }
        extraParams.put(key, value);
    }

    /**
     * 获取币种，默认为CNY
     * 
     * @return 币种
     */
    public String getCurrency() {
        return currency != null ? currency : "CNY";
    }

    /**
     * 获取过期时长（分钟），默认30分钟
     * 
     * @return 过期时长
     */
    public Integer getExpireMinutes() {
        return expireMinutes != null ? expireMinutes : 30;
    }
}
