package net.summerfarm.payment.sdk.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.payment.sdk.common.enums.ErrorCode;

import java.io.Serializable;

/**
 * 统一错误信息模型
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnifiedError implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private String code;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 详细错误信息
     */
    private String detailMessage;

    /**
     * 渠道名称（如果是渠道相关错误）
     */
    private String channelName;

    /**
     * 渠道返回的原始错误码
     */
    private String channelErrorCode;

    /**
     * 渠道返回的原始错误信息
     */
    private String channelErrorMessage;

    /**
     * 错误发生时间戳
     */
    private Long timestamp;

    /**
     * 从ErrorCode创建UnifiedError
     * 
     * @param errorCode 错误码枚举
     * @return UnifiedError
     */
    public static UnifiedError from(ErrorCode errorCode) {
        return UnifiedError.builder()
                .code(errorCode.getCode())
                .message(errorCode.getMessage())
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 从ErrorCode和详细信息创建UnifiedError
     * 
     * @param errorCode 错误码枚举
     * @param detailMessage 详细错误信息
     * @return UnifiedError
     */
    public static UnifiedError from(ErrorCode errorCode, String detailMessage) {
        return UnifiedError.builder()
                .code(errorCode.getCode())
                .message(errorCode.getMessage())
                .detailMessage(detailMessage)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 从渠道错误信息创建UnifiedError
     * 
     * @param errorCode 错误码枚举
     * @param channelName 渠道名称
     * @param channelErrorCode 渠道错误码
     * @param channelErrorMessage 渠道错误信息
     * @return UnifiedError
     */
    public static UnifiedError fromChannel(ErrorCode errorCode, String channelName, 
                                         String channelErrorCode, String channelErrorMessage) {
        return UnifiedError.builder()
                .code(errorCode.getCode())
                .message(errorCode.getMessage())
                .channelName(channelName)
                .channelErrorCode(channelErrorCode)
                .channelErrorMessage(channelErrorMessage)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
