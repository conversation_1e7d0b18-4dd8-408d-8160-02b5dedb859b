package net.summerfarm.payment.gateway.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统一支付状态枚举
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@Getter
@AllArgsConstructor
public enum PaymentStatus {

    /**
     * 支付成功
     */
    SUCCESS("SUCCESS", "支付成功"),

    /**
     * 支付失败
     */
    FAILED("FAILED", "支付失败"),

    /**
     * 支付处理中/等待中
     */
    PENDING("PENDING", "支付处理中"),

    /**
     * 支付已关闭
     */
    CLOSED("CLOSED", "支付已关闭"),

    /**
     * 支付已取消
     */
    CANCELLED("CANCELLED", "支付已取消"),

    /**
     * 支付已退款
     */
    REFUNDED("REFUNDED", "支付已退款"),

    /**
     * 部分退款
     */
    PARTIAL_REFUNDED("PARTIAL_REFUNDED", "部分退款"),

    /**
     * 未知状态
     */
    UNKNOWN("UNKNOWN", "未知状态");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return PaymentStatus
     */
    public static PaymentStatus fromCode(String code) {
        for (PaymentStatus status : PaymentStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }

    /**
     * 是否为最终状态（不会再变化的状态）
     * 
     * @return boolean
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED || this == CLOSED || 
               this == CANCELLED || this == REFUNDED;
    }

    /**
     * 是否为成功状态
     * 
     * @return boolean
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * 是否为失败状态
     * 
     * @return boolean
     */
    public boolean isFailed() {
        return this == FAILED;
    }

    /**
     * 是否为处理中状态
     * 
     * @return boolean
     */
    public boolean isPending() {
        return this == PENDING;
    }
}
