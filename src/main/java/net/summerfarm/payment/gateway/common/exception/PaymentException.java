package net.summerfarm.payment.gateway.common.exception;

import net.summerfarm.payment.gateway.common.enums.ErrorCode;

/**
 * 支付网关基础异常类
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
public class PaymentException extends RuntimeException {

    /**
     * 错误码
     */
    private final ErrorCode errorCode;

    /**
     * 详细错误信息
     */
    private final String detailMessage;

    public PaymentException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.detailMessage = null;
    }

    public PaymentException(ErrorCode errorCode, String detailMessage) {
        super(errorCode.getMessage() + ": " + detailMessage);
        this.errorCode = errorCode;
        this.detailMessage = detailMessage;
    }

    public PaymentException(ErrorCode errorCode, String detailMessage, Throwable cause) {
        super(errorCode.getMessage() + ": " + detailMessage, cause);
        this.errorCode = errorCode;
        this.detailMessage = detailMessage;
    }

    public PaymentException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode;
        this.detailMessage = null;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    public String getCode() {
        return errorCode.getCode();
    }
}
