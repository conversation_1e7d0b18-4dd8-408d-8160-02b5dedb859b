<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.payment.routing.dao.PaymentRuleRoutingDAO">
  <resultMap id="BaseResultMap" type="net.summerfarm.payment.routing.model.domain.PaymentRuleRouting">
    <!--@mbg.generated-->
    <!--@Table payment_rule_routing-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="route_key" jdbcType="VARCHAR" property="routeKey" />
    <result column="route_value" jdbcType="VARCHAR" property="routeValue" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, business_line, route_key, route_value, rule_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from payment_rule_routing
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from payment_rule_routing
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.payment.routing.model.domain.PaymentRuleRouting" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into payment_rule_routing (tenant_id, business_line, route_key, 
      route_value, rule_id)
    values (#{tenantId,jdbcType=BIGINT}, #{businessLine,jdbcType=VARCHAR}, #{routeKey,jdbcType=VARCHAR}, 
      #{routeValue,jdbcType=VARCHAR}, #{ruleId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.payment.routing.model.domain.PaymentRuleRouting" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into payment_rule_routing
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="businessLine != null">
        business_line,
      </if>
      <if test="routeKey != null">
        route_key,
      </if>
      <if test="routeValue != null">
        route_value,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="businessLine != null">
        #{businessLine,jdbcType=VARCHAR},
      </if>
      <if test="routeKey != null">
        #{routeKey,jdbcType=VARCHAR},
      </if>
      <if test="routeValue != null">
        #{routeValue,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.payment.routing.model.domain.PaymentRuleRouting">
    <!--@mbg.generated-->
    update payment_rule_routing
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="businessLine != null">
        business_line = #{businessLine,jdbcType=VARCHAR},
      </if>
      <if test="routeKey != null">
        route_key = #{routeKey,jdbcType=VARCHAR},
      </if>
      <if test="routeValue != null">
        route_value = #{routeValue,jdbcType=VARCHAR},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.payment.routing.model.domain.PaymentRuleRouting">
    <!--@mbg.generated-->
    update payment_rule_routing
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      business_line = #{businessLine,jdbcType=VARCHAR},
      route_key = #{routeKey,jdbcType=VARCHAR},
      route_value = #{routeValue,jdbcType=VARCHAR},
      rule_id = #{ruleId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listRuleRouting" resultMap="BaseResultMap">
    select
    r.id, r.tenant_id, r.business_line, r.route_key, r.route_value, r.rule_id
    from payment_rule_routing r
    left join payment_rule pr on r.rule_id = pr.id
    <where>
      <if test="tenantId != null">
        and r.tenant_id = #{tenantId}
      </if>
      <if test="businessLine != null">
        and r.business_line = #{businessLine}
      </if>
      <if test="routeKey != null">
        and r.route_key = #{routeKey}
      </if>
      <if test="routeValue != null and routeValue.size() != 0">
        and r.route_value in
        <foreach collection="routeValue" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="paymentMethod != null and paymentMethod != ''">
        and pr.payment_method = #{paymentMethod}
      </if>
      <if test="platform != null and platform != ''">
        and pr.platform = #{platform}
      </if>
    </where>
  </select>

  <select id="listByRuleIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from payment_rule_routing
    where rule_id in
    <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
      #{ruleId}
    </foreach>
  </select>

  <select id="listByRuleIdsAndRouteKeys" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from payment_rule_routing
    where rule_id in
    <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
      #{ruleId}
    </foreach>
    and route_key in
    <foreach collection="routeKeys" item="routeKey" open="(" separator="," close=")">
      #{routeKey}
    </foreach>
  </select>

  <insert id="batchInsert">
    insert into payment_rule_routing (tenant_id, business_line, route_key, route_value, rule_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tenantId}, #{item.businessLine}, #{item.routeKey}, #{item.routeValue}, #{item.ruleId})
    </foreach>
  </insert>

  <delete id="deleteByIds">
    delete from payment_rule_routing
    where id in
    <foreach collection="list" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
</mapper>