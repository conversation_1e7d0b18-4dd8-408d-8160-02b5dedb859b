<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.payment.routing.dao.PaymentChannelDAO">
  <resultMap id="BaseResultMap" type="net.summerfarm.payment.routing.model.domain.PaymentChannel">
    <!--@mbg.generated-->
    <!--@Table payment_channel-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="company_entity" jdbcType="VARCHAR" property="companyEntity" />
    <result column="operator_admin_id" jdbcType="BIGINT" property="operatorAdminId" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="merchant_no" jdbcType="VARCHAR" property="merchantNo" />
    <result column="sub_merchant_no" jdbcType="VARCHAR" property="subMerchantNo" />
    <result column="public_key" jdbcType="VARCHAR" property="publicKey" />
    <result column="private_key" jdbcType="VARCHAR" property="privateKey" />
    <result column="secret" jdbcType="VARCHAR" property="secret" />
    <result column="extra_config" jdbcType="VARCHAR" property="extraConfig" />
    <result column="company_account_id" jdbcType="INTEGER" property="companyAccountId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, business_line, channel_name, company_entity, operator_admin_id, operate_time, 
    `status`, merchant_no, sub_merchant_no, public_key, private_key, secret, extra_config, company_account_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from payment_channel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from payment_channel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.payment.routing.model.domain.PaymentChannel" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into payment_channel (tenant_id, business_line, channel_name, 
      company_entity, operator_admin_id, operate_time, 
      `status`, merchant_no, sub_merchant_no, 
      public_key, private_key, secret, 
      extra_config, company_account_id)
    values (#{tenantId,jdbcType=BIGINT}, #{businessLine,jdbcType=VARCHAR}, #{channelName,jdbcType=VARCHAR}, 
      #{companyEntity,jdbcType=VARCHAR}, #{operatorAdminId,jdbcType=BIGINT}, #{operateTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{merchantNo,jdbcType=VARCHAR}, #{subMerchantNo,jdbcType=VARCHAR}, 
      #{publicKey,jdbcType=VARCHAR}, #{privateKey,jdbcType=VARCHAR}, #{secret,jdbcType=VARCHAR}, 
      #{extraConfig,jdbcType=VARCHAR}, #{companyAccountId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.payment.routing.model.domain.PaymentChannel" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into payment_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="businessLine != null">
        business_line,
      </if>
      <if test="channelName != null">
        channel_name,
      </if>
      <if test="companyEntity != null">
        company_entity,
      </if>
      <if test="operatorAdminId != null">
        operator_admin_id,
      </if>
      <if test="operateTime != null">
        operate_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="merchantNo != null">
        merchant_no,
      </if>
      <if test="subMerchantNo != null">
        sub_merchant_no,
      </if>
      <if test="publicKey != null">
        public_key,
      </if>
      <if test="privateKey != null">
        private_key,
      </if>
      <if test="secret != null">
        secret,
      </if>
      <if test="extraConfig != null">
        extra_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="businessLine != null">
        #{businessLine,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="companyEntity != null">
        #{companyEntity,jdbcType=VARCHAR},
      </if>
      <if test="operatorAdminId != null">
        #{operatorAdminId,jdbcType=BIGINT},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="merchantNo != null">
        #{merchantNo,jdbcType=VARCHAR},
      </if>
      <if test="subMerchantNo != null">
        #{subMerchantNo,jdbcType=VARCHAR},
      </if>
      <if test="publicKey != null">
        #{publicKey,jdbcType=VARCHAR},
      </if>
      <if test="privateKey != null">
        #{privateKey,jdbcType=VARCHAR},
      </if>
      <if test="secret != null">
        #{secret,jdbcType=VARCHAR},
      </if>
      <if test="extraConfig != null">
        #{extraConfig,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.payment.routing.model.domain.PaymentChannel">
    <!--@mbg.generated-->
    update payment_channel
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="businessLine != null">
        business_line = #{businessLine,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="companyEntity != null">
        company_entity = #{companyEntity,jdbcType=VARCHAR},
      </if>
      <if test="operatorAdminId != null">
        operator_admin_id = #{operatorAdminId,jdbcType=BIGINT},
      </if>
      <if test="operateTime != null">
        operate_time = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="merchantNo != null">
        merchant_no = #{merchantNo,jdbcType=VARCHAR},
      </if>
      <if test="subMerchantNo != null">
        sub_merchant_no = #{subMerchantNo,jdbcType=VARCHAR},
      </if>
      <if test="publicKey != null">
        public_key = #{publicKey,jdbcType=VARCHAR},
      </if>
      <if test="privateKey != null">
        private_key = #{privateKey,jdbcType=VARCHAR},
      </if>
      <if test="secret != null">
        secret = #{secret,jdbcType=VARCHAR},
      </if>
      <if test="extraConfig != null">
        extra_config = #{extraConfig,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.payment.routing.model.domain.PaymentChannel">
    <!--@mbg.generated-->
    update payment_channel
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      business_line = #{businessLine,jdbcType=VARCHAR},
      channel_name = #{channelName,jdbcType=VARCHAR},
      company_entity = #{companyEntity,jdbcType=VARCHAR},
      operator_admin_id = #{operatorAdminId,jdbcType=BIGINT},
      operate_time = #{operateTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      merchant_no = #{merchantNo,jdbcType=VARCHAR},
      sub_merchant_no = #{subMerchantNo,jdbcType=VARCHAR},
      public_key = #{publicKey,jdbcType=VARCHAR},
      private_key = #{privateKey,jdbcType=VARCHAR},
      secret = #{secret,jdbcType=VARCHAR},
      extra_config = #{extraConfig,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listChannel" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from payment_channel
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId,jdbcType=BIGINT}
      </if>
      <if test="businessLine != null">
        and business_line = #{businessLine,jdbcType=VARCHAR}
      </if>
      <if test="channelName != null">
        and channel_name like concat(#{channelName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="preciseChannelName != null and preciseChannelName != ''">
        and channel_name = #{preciseChannelName,jdbcType=VARCHAR}
      </if>
      <if test="companyEntity != null">
        and company_entity = #{companyEntity,jdbcType=VARCHAR}
      </if>
      <if test="status != null">
        and `status` = #{status,jdbcType=TINYINT}
      </if>
      <if test="statusList != null and statusList.size() != 0">
        and `status` in
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
          #{status}
        </foreach>
      </if>
      <if test="merchantNo != null">
        and merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
    </where>
    order by id
  </select>

  <select id="selectByUnique" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from payment_channel
    where tenant_id = #{tenantId,jdbcType=BIGINT} and business_line = #{businessLine,jdbcType=VARCHAR} and channel_name = #{channelName,jdbcType=VARCHAR} and company_entity = #{companyEntity,jdbcType=VARCHAR} and merchant_no = #{merchantNo,jdbcType=VARCHAR}
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from payment_channel
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

  <select id="queryCompanyEntities" resultType="java.lang.String">
    select distinct company_entity
    from payment_channel
    <where>
      <if test="tenantId != null">
        and tenant_id = #{tenantId,jdbcType=BIGINT}
      </if>
      <if test="businessLine != null">
        and business_line = #{businessLine,jdbcType=VARCHAR}
      </if>
      <if test="statusList != null and statusList.size() != 0">
        and `status` in
        <foreach collection="statusList" item="status" open="(" separator="," close=")">
          #{status}
        </foreach>
      </if>
    </where>
  </select>
</mapper>