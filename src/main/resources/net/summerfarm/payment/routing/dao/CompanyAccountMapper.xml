<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.payment.routing.dao.CompanyAccountMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.payment.routing.model.domain.CompanyAccount">
    <!--@mbg.generated-->
    <!--@Table company_account-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="wx_account_info" jdbcType="VARCHAR" property="wxAccountInfo" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="addtime" jdbcType="TIMESTAMP" property="addtime" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="mch_app_id" jdbcType="VARCHAR" property="mchAppId" />
    <result column="mchx_app_id" jdbcType="VARCHAR" property="mchxAppId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, company_name, wx_account_info, admin_id, addtime, channel, mch_app_id, mchx_app_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from company_account
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from company_account
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.payment.routing.model.domain.CompanyAccount" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into company_account (company_name, wx_account_info, admin_id, 
      addtime, channel, mch_app_id, 
      mchx_app_id)
    values (#{companyName,jdbcType=VARCHAR}, #{wxAccountInfo,jdbcType=VARCHAR}, #{adminId,jdbcType=INTEGER}, 
      #{addtime,jdbcType=TIMESTAMP}, #{channel,jdbcType=INTEGER}, #{mchAppId,jdbcType=VARCHAR}, 
      #{mchxAppId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.payment.routing.model.domain.CompanyAccount" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into company_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyName != null">
        company_name,
      </if>
      <if test="wxAccountInfo != null">
        wx_account_info,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="addtime != null">
        addtime,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="mchAppId != null">
        mch_app_id,
      </if>
      <if test="mchxAppId != null">
        mchx_app_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="wxAccountInfo != null">
        #{wxAccountInfo,jdbcType=VARCHAR},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="addtime != null">
        #{addtime,jdbcType=TIMESTAMP},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="mchAppId != null">
        #{mchAppId,jdbcType=VARCHAR},
      </if>
      <if test="mchxAppId != null">
        #{mchxAppId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.payment.routing.model.domain.CompanyAccount">
    <!--@mbg.generated-->
    update company_account
    <set>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="wxAccountInfo != null">
        wx_account_info = #{wxAccountInfo,jdbcType=VARCHAR},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="addtime != null">
        addtime = #{addtime,jdbcType=TIMESTAMP},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="mchAppId != null">
        mch_app_id = #{mchAppId,jdbcType=VARCHAR},
      </if>
      <if test="mchxAppId != null">
        mchx_app_id = #{mchxAppId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.payment.routing.model.domain.CompanyAccount">
    <!--@mbg.generated-->
    update company_account
    set company_name = #{companyName,jdbcType=VARCHAR},
      wx_account_info = #{wxAccountInfo,jdbcType=VARCHAR},
      admin_id = #{adminId,jdbcType=INTEGER},
      addtime = #{addtime,jdbcType=TIMESTAMP},
      channel = #{channel,jdbcType=INTEGER},
      mch_app_id = #{mchAppId,jdbcType=VARCHAR},
      mchx_app_id = #{mchxAppId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>