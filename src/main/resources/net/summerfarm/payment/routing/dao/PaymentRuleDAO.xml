<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.payment.routing.dao.PaymentRuleDAO">
  <resultMap id="BaseResultMap" type="net.summerfarm.payment.routing.model.domain.PaymentRule">
    <!--@mbg.generated-->
    <!--@Table payment_rule-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
    <result column="scene_name" jdbcType="VARCHAR" property="sceneName" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, business_line, scene_name, platform, payment_method, channel_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from payment_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from payment_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.payment.routing.model.domain.PaymentRule" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into payment_rule (tenant_id, business_line, scene_name, 
      platform, payment_method, channel_id
      )
    values (#{tenantId,jdbcType=BIGINT}, #{businessLine,jdbcType=VARCHAR}, #{sceneName,jdbcType=VARCHAR}, 
      #{platform,jdbcType=VARCHAR}, #{paymentMethod,jdbcType=VARCHAR}, #{channelId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.payment.routing.model.domain.PaymentRule" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into payment_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="businessLine != null">
        business_line,
      </if>
      <if test="sceneName != null">
        scene_name,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="paymentMethod != null">
        payment_method,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="businessLine != null">
        #{businessLine,jdbcType=VARCHAR},
      </if>
      <if test="sceneName != null">
        #{sceneName,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="paymentMethod != null">
        #{paymentMethod,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.payment.routing.model.domain.PaymentRule">
    <!--@mbg.generated-->
    update payment_rule
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="businessLine != null">
        business_line = #{businessLine,jdbcType=VARCHAR},
      </if>
      <if test="sceneName != null">
        scene_name = #{sceneName,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="paymentMethod != null">
        payment_method = #{paymentMethod,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.payment.routing.model.domain.PaymentRule">
    <!--@mbg.generated-->
    update payment_rule
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      business_line = #{businessLine,jdbcType=VARCHAR},
      scene_name = #{sceneName,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      payment_method = #{paymentMethod,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="countByChannelId" resultType="int">
    select count(1)
    from payment_rule
    where channel_id = #{channelId}
  </select>

  <select id="listRule" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from payment_rule
    <where>
        <if test="tenantId != null">
            and tenant_id = #{tenantId}
        </if>
        <if test="businessLine != null">
            and business_line = #{businessLine}
        </if>
        <if test="sceneName != null">
            and scene_name = #{sceneName}
        </if>
        <if test="platform != null">
            and platform = #{platform}
        </if>
        <if test="paymentMethod != null">
            and payment_method = #{paymentMethod}
        </if>
        <if test="channelId != null">
          and channel_id = #{channelId}
        </if>
        <if test="ids != null and ids.size() != 0">
            and id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </where>
    order by id
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from payment_rule
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
    </foreach>
  </select>

  <select id="queryUsableMethods" resultMap="BaseResultMap">
    select r.id, r.tenant_id, r.business_line, r.scene_name, r.platform, r.payment_method, r.channel_id
    from payment_rule r
    join payment_rule_routing t on r.id = t.rule_id and r.tenant_id = t.tenant_id
    <where>
      <if test="tenantId != null">
        and r.tenant_id = #{tenantId}
        </if>
        <if test="businessLine != null">
        and r.business_line = #{businessLine}
        </if>
        <if test="platform != null">
        and r.platform = #{platform}
        </if>
        <if test="routeKey != null">
        and t.route_key = #{routeKey}
        </if>
        <if test="routeValue != null">
        and t.route_value = #{routeValue}
        </if>
      <if test="paymentMethod != null">
        and r.payment_method = #{paymentMethod}
      </if>
    </where>
  </select>

  <select id="selectEarliestRule" resultMap="BaseResultMap">
    select r.id, r.tenant_id, r.business_line, r.scene_name, r.platform, r.payment_method, r.channel_id
    from payment_rule r
    left join payment_channel c on r.channel_id = c.id
    <where>
      <if test="tenantId != null">
        and r.tenant_id = #{tenantId}
      </if>
      <if test="businessLine != null">
        and r.business_line = #{businessLine}
      </if>
      <if test="platform != null">
        and r.platform = #{platform}
      </if>
      <if test="paymentMethod != null">
        and r.payment_method = #{paymentMethod}
      </if>
      <if test="channelName != null and channelName != ''">
        and c.channel_name = #{channelName}
      </if>
      <if test="status != null">
        and c.status = #{status}
        </if>
    </where>
    order by c.create_time
    limit 1
  </select>
</mapper>