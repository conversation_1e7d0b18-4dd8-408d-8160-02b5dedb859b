package net.summerfarm.payment.trade.adapter;

import net.summerfarm.payment.trade.adapter.impl.DinPayChannelAdapter;
import net.summerfarm.payment.trade.common.enums.FeatureType;
import net.summerfarm.payment.trade.common.enums.PaymentStatus;
import net.summerfarm.payment.trade.model.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DinPay适配器单元测试
 * 测试新架构中适配器的参数转换功能
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@ExtendWith(MockitoExtension.class)
class DinPayAdapterTest {

    private DinPayChannelAdapter adapter;

    @BeforeEach
    void setUp() {
        adapter = new DinPayChannelAdapter();
    }

    @Test
    void testGetChannelInfo() {
        assertEquals("dinpay", adapter.getChannelCode());
        assertEquals("DinPay智付", adapter.getChannelName());
    }

    @Test
    void testGetSupportedFeatures() {
        Set<FeatureType> features = adapter.getSupportedFeatures();
        
        assertNotNull(features);
        assertTrue(features.contains(FeatureType.H5_PAYMENT_SUPPORT));
        assertTrue(features.contains(FeatureType.MINI_APP_PAYMENT_SUPPORT));
        assertTrue(features.contains(FeatureType.QR_CODE_PAYMENT_SUPPORT));
        assertTrue(features.contains(FeatureType.REFUND_SUPPORT));
        assertTrue(features.contains(FeatureType.ALLOW_ONE_CENT_ORDER));
    }

    @Test
    void testConvertPaymentRequest() {
        // 准备测试数据
        UnifiedPaymentRequest request = buildTestPaymentRequest();
        
        // 测试适配器转换请求
        Object channelRequest = adapter.convertPaymentRequest(request);
        
        // 验证转换结果
        assertNotNull(channelRequest);
        assertTrue(channelRequest instanceof DinPayChannelAdapter.DinPayPaymentRequest);
        
        DinPayChannelAdapter.DinPayPaymentRequest dinPayRequest = 
                (DinPayChannelAdapter.DinPayPaymentRequest) channelRequest;
        
        assertEquals("TEST_MERCHANT_001", dinPayRequest.getMerchantNo());
        assertEquals("TEST_ORDER_001", dinPayRequest.getOrderNo());
        assertEquals(9999, dinPayRequest.getAmount().intValue());
        assertEquals("测试订单", dinPayRequest.getSubject());
        assertEquals("WECHAT_MP", dinPayRequest.getPaymentMethod());
        assertEquals("user_123456", dinPayRequest.getOpenid());
    }

    @Test
    void testConvertPaymentResponse() {
        // 准备测试数据
        UnifiedPaymentRequest originalRequest = buildTestPaymentRequest();
        DinPayChannelAdapter.DinPayPaymentResponse channelResponse = 
                DinPayChannelAdapter.DinPayPaymentResponse.builder()
                        .code("SUCCESS")
                        .message("支付成功")
                        .orderNo("TEST_ORDER_001")
                        .dinpayOrderNo("DP123456789")
                        .qrCode("weixin://wxpay/bizpayurl?pr=test123")
                        .build();
        
        // 测试适配器转换响应
        UnifiedPaymentResult result = adapter.convertPaymentResponse(channelResponse, originalRequest);
        
        // 验证转换结果
        assertNotNull(result);
        assertEquals(PaymentStatus.PENDING, result.getStatus());
        assertEquals("TEST_ORDER_001", result.getPaymentNo());
        assertEquals("DP123456789", result.getPaymentOrderId());
        assertNotNull(result.getCredential());
        assertEquals("QR_CODE", result.getCredential().getType());
        assertEquals("weixin://wxpay/bizpayurl?pr=test123", result.getCredential().getContent());
    }

    @Test
    void testPaymentMethodMapping() {
        // 测试微信小程序支付方式映射
        UnifiedPaymentRequest request = buildTestPaymentRequest();
        request.setPaymentMethod("wechat");
        request.setPlatform("miniapp");
        
        Object channelRequest = adapter.convertPaymentRequest(request);
        DinPayChannelAdapter.DinPayPaymentRequest dinPayRequest = 
                (DinPayChannelAdapter.DinPayPaymentRequest) channelRequest;
        
        assertEquals("WECHAT_MP", dinPayRequest.getPaymentMethod());
        
        // 测试微信H5支付方式映射
        request.setPlatform("h5");
        channelRequest = adapter.convertPaymentRequest(request);
        dinPayRequest = (DinPayChannelAdapter.DinPayPaymentRequest) channelRequest;
        
        assertEquals("WECHAT_H5", dinPayRequest.getPaymentMethod());
        
        // 测试支付宝支付方式映射
        request.setPaymentMethod("alipay");
        channelRequest = adapter.convertPaymentRequest(request);
        dinPayRequest = (DinPayChannelAdapter.DinPayPaymentRequest) channelRequest;
        
        assertEquals("ALIPAY", dinPayRequest.getPaymentMethod());
    }

    @Test
    void testValidateRequest() {
        // 测试基础验证
        UnifiedPaymentRequest validRequest = buildTestPaymentRequest();
        
        // 这些调用不应该抛出异常
        assertDoesNotThrow(() -> {
            adapter.validateRequest(validRequest);
        });
        
        // 测试无效请求
        assertThrows(IllegalArgumentException.class, () -> {
            adapter.validateRequest(null);
        });
        
        UnifiedPaymentRequest invalidRequest = buildTestPaymentRequest();
        invalidRequest.setPaymentNo(null);
        assertThrows(IllegalArgumentException.class, () -> {
            adapter.validateRequest(invalidRequest);
        });
    }

    /**
     * 构建测试用的支付请求
     */
    private UnifiedPaymentRequest buildTestPaymentRequest() {
        // 渠道配置
        ChannelConfig channelConfig = ChannelConfig.builder()
                .channelCode("dinpay")
                .channelName("DinPay智付")
                .merchantNo("TEST_MERCHANT_001")
                .privateKey("test_private_key")
                .publicKey("test_public_key")
                .secretKey("test_secret_key")
                .build();

        // 支付者信息
        PayerInfo payer = PayerInfo.builder()
                .userId("user_123456")
                .build();

        return UnifiedPaymentRequest.builder()
                .paymentNo("TEST_ORDER_001")
                .description("测试订单")
                .attach("test_attach_data")
                .totalAmount(9999) // 99.99元，单位分
                .currency("CNY")
                .notifyUrl("https://test.example.com/notify")
                .payer(payer)
                .tenantId(1001L)
                .businessLine("saas")
                .paymentMethod("wechat")
                .platform("miniapp")
                .channelConfig(channelConfig)
                .build();
    }
}
