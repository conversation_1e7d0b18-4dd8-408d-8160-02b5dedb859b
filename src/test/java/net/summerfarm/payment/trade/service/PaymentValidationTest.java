package net.summerfarm.payment.trade.service;

import net.summerfarm.payment.trade.adapter.impl.DinPayChannelAdapter;
import net.summerfarm.payment.trade.client.impl.DinPayChannelClient;
import net.summerfarm.payment.trade.common.enums.PaymentStatus;
import net.summerfarm.payment.trade.common.exception.PaymentException;
import net.summerfarm.payment.trade.model.*;
import net.summerfarm.payment.trade.service.impl.PaymentClientServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 支付参数验证测试
 * 验证参数验证的改进：在最开始就做完整验证，后续安全使用
 * 
 * <AUTHOR> Agent
 * @date 2025-08-08
 */
@ExtendWith(MockitoExtension.class)
class PaymentValidationTest {

    private PaymentClientServiceImpl paymentClientService;

    @BeforeEach
    void setUp() {
        paymentClientService = new PaymentClientServiceImpl();
        
        // 注册适配器和客户端
        paymentClientService.registerAdapter(new DinPayChannelAdapter());
        paymentClientService.registerClient(new DinPayChannelClient());
    }

    @Test
    void testValidPaymentRequest() {
        // 测试有效的支付请求
        UnifiedPaymentRequest request = buildValidPaymentRequest();
        
        UnifiedPaymentResult result = paymentClientService.pay(request);
        
        assertNotNull(result);
        assertEquals(PaymentStatus.PENDING, result.getStatus());
        assertEquals("TEST_ORDER_001", result.getPaymentNo());
    }

    @Test
    void testNullPaymentRequest() {
        // 测试空请求
        PaymentException exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(null);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("支付请求不能为空"));
    }

    @Test
    void testEmptyPaymentNo() {
        // 测试空订单号
        UnifiedPaymentRequest request = buildValidPaymentRequest();
        request.setPaymentNo(null);
        
        PaymentException exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("商户订单号不能为空"));
        
        // 测试空字符串订单号
        request.setPaymentNo("   ");
        exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("商户订单号不能为空"));
    }

    @Test
    void testInvalidAmount() {
        // 测试空金额
        UnifiedPaymentRequest request = buildValidPaymentRequest();
        request.setTotalAmount(null);
        
        PaymentException exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("支付金额必须大于0"));
        
        // 测试负金额
        request.setTotalAmount(-100);
        exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("支付金额必须大于0"));
        
        // 测试零金额
        request.setTotalAmount(0);
        exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("支付金额必须大于0"));
    }

    @Test
    void testEmptyDescription() {
        // 测试空商品描述
        UnifiedPaymentRequest request = buildValidPaymentRequest();
        request.setDescription(null);
        
        PaymentException exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("商品描述不能为空"));
    }

    @Test
    void testEmptyPaymentMethod() {
        // 测试空支付方式
        UnifiedPaymentRequest request = buildValidPaymentRequest();
        request.setPaymentMethod(null);
        
        PaymentException exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("支付方式不能为空"));
    }

    @Test
    void testEmptyPlatform() {
        // 测试空平台
        UnifiedPaymentRequest request = buildValidPaymentRequest();
        request.setPlatform(null);
        
        PaymentException exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("应用平台不能为空"));
    }

    @Test
    void testNullChannelConfig() {
        // 测试空渠道配置
        UnifiedPaymentRequest request = buildValidPaymentRequest();
        request.setChannelConfig(null);
        
        PaymentException exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("渠道配置不能为空"));
    }

    @Test
    void testEmptyChannelCode() {
        // 测试空渠道标识符
        UnifiedPaymentRequest request = buildValidPaymentRequest();
        request.getChannelConfig().setChannelCode(null);
        
        PaymentException exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("渠道标识符不能为空"));
    }

    @Test
    void testNullPayer() {
        // 测试空支付者信息
        UnifiedPaymentRequest request = buildValidPaymentRequest();
        request.setPayer(null);
        
        PaymentException exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("支付者信息不能为空"));
    }

    @Test
    void testEmptyUserId() {
        // 测试空用户ID
        UnifiedPaymentRequest request = buildValidPaymentRequest();
        request.getPayer().setUserId(null);
        
        PaymentException exception = assertThrows(PaymentException.class, () -> {
            paymentClientService.pay(request);
        });
        
        assertEquals("1002", exception.getCode());
        assertTrue(exception.getMessage().contains("支付者用户ID不能为空"));
    }

    /**
     * 构建有效的支付请求
     */
    private UnifiedPaymentRequest buildValidPaymentRequest() {
        ChannelConfig channelConfig = ChannelConfig.builder()
                .channelCode("dinpay")
                .channelName("DinPay智付")
                .merchantNo("TEST_MERCHANT_001")
                .privateKey("test_private_key")
                .publicKey("test_public_key")
                .secretKey("test_secret_key")
                .build();

        PayerInfo payer = PayerInfo.builder()
                .userId("user_123456")
                .build();

        return UnifiedPaymentRequest.builder()
                .paymentNo("TEST_ORDER_001")
                .description("测试订单")
                .totalAmount(9999)
                .currency("CNY")
                .notifyUrl("https://test.example.com/notify")
                .payer(payer)
                .tenantId(1001L)
                .businessLine("saas")
                .paymentMethod("wechat")
                .platform("miniapp")
                .channelConfig(channelConfig)
                .build();
    }
}
