# 支付交易架构设计文档

## 架构概述

本文档描述了支付交易模块的新架构设计，采用**适配器 + 客户端分离**的模式，实现了更清晰的职责分离和更好的可维护性。

## 核心设计理念

### 1. 职责分离
- **ChannelAdapter**: 专注于参数转换和特性发现
- **ChannelClient**: 专注于API调用和网络通信
- **PaymentClientService**: 协调适配器和客户端，处理业务流程

### 2. 单一职责原则
每个组件都有明确的单一职责，避免了之前适配器既要转换参数又要调用API的问题。

## 架构组件

### 1. 适配器接口 (ChannelAdapter)

```java
public interface ChannelAdapter {
    String getChannelCode();
    String getChannelName();
    Set<FeatureType> getSupportedFeatures();
    
    // 参数转换方法
    Object convertPaymentRequest(UnifiedPaymentRequest request);
    UnifiedPaymentResult convertPaymentResponse(Object channelResponse, UnifiedPaymentRequest originalRequest);
    
    // 扩展转换方法
    Object convertRefundRequest(Object request);
    Object convertRefundResponse(Object channelResponse, Object originalRequest);
    Object convertQueryRequest(Object request);
    Object convertQueryResponse(Object channelResponse, Object originalRequest);
}
```

**职责**：
- 声明渠道支持的特性
- 将统一模型转换为渠道特定模型
- 将渠道响应转换为统一模型
- 验证请求参数的合法性

### 2. 客户端接口 (ChannelClient)

```java
public interface ChannelClient {
    String getChannelCode();
    String getChannelName();
    
    // API调用方法
    Object pay(Object channelRequest) throws Exception;
    Object refund(Object channelRequest) throws Exception;
    Object query(Object channelRequest) throws Exception;
    Object close(Object channelRequest) throws Exception;
    
    // 辅助方法
    boolean healthCheck();
    int getTimeoutMillis();
}
```

**职责**：
- 与渠道API进行具体的网络通信
- 处理HTTP请求/响应
- 处理签名和验签
- 处理网络异常和重试
- 提供健康检查功能

### 3. 支付服务 (PaymentClientService)

```java
public class PaymentClientServiceImpl implements PaymentClientService {
    public UnifiedPaymentResult pay(UnifiedPaymentRequest request) {
        // 1. 获取适配器和客户端
        ChannelAdapter adapter = getAdapter(channelCode);
        ChannelClient client = getClient(channelCode);
        
        // 2. 转换请求参数
        Object channelRequest = adapter.convertPaymentRequest(request);
        
        // 3. 调用渠道API
        Object channelResponse = client.pay(channelRequest);
        
        // 4. 转换响应结果
        return adapter.convertPaymentResponse(channelResponse, request);
    }
}
```

**职责**：
- 协调适配器和客户端
- 处理业务流程和异常
- 进行特性检查和参数验证
- 管理适配器和客户端的注册

## 实现示例

### DinPay适配器实现

```java
@Component
public class DinPayChannelAdapter implements ChannelAdapter {
    
    @Override
    public Object convertPaymentRequest(UnifiedPaymentRequest request) {
        // 只做参数转换，不调用API
        return DinPayPaymentRequest.builder()
                .merchantNo(request.getChannelConfig().getMerchantNo())
                .orderNo(request.getPaymentNo())
                .amount(request.getTotalAmount())
                .subject(request.getDescription())
                .paymentMethod(mapPaymentMethod(request.getPaymentMethod(), request.getPlatform()))
                .openid(request.getPayer().getOpenid())
                .build();
    }
    
    @Override
    public UnifiedPaymentResult convertPaymentResponse(Object channelResponse, UnifiedPaymentRequest originalRequest) {
        // 只做响应转换
        DinPayPaymentResponse response = (DinPayPaymentResponse) channelResponse;
        return UnifiedPaymentResult.pending(
                originalRequest.getPaymentNo(),
                response.getDinpayOrderNo(),
                buildCredential(response),
                CHANNEL_NAME
        );
    }
}
```

### DinPay客户端实现

```java
@Component
public class DinPayChannelClient implements ChannelClient {
    
    @Override
    public Object pay(Object channelRequest) throws Exception {
        // 只做API调用，不做参数转换
        DinPayPaymentRequest request = (DinPayPaymentRequest) channelRequest;
        
        // 1. 构建HTTP请求
        // 2. 设置签名
        // 3. 发送请求
        // 4. 解析响应
        // 5. 验证签名
        
        return callDinPayApi(request);
    }
}
```

## 架构优势

### 1. 更好的可测试性
- **适配器测试**: 可以单独测试参数转换逻辑，不需要真实的API调用
- **客户端测试**: 可以单独测试API调用逻辑，使用Mock数据
- **集成测试**: 可以组合测试完整流程

### 2. 更好的可维护性
- **参数变化**: 只需修改适配器的转换逻辑
- **API变化**: 只需修改客户端的调用逻辑
- **业务变化**: 只需修改服务层的协调逻辑

### 3. 更好的可扩展性
- **新增渠道**: 只需实现对应的适配器和客户端
- **功能扩展**: 可以独立扩展转换逻辑或API调用逻辑
- **复用性**: 同一个客户端可以被多个适配器使用

### 4. 更好的错误处理
- **转换错误**: 在适配器层捕获和处理
- **网络错误**: 在客户端层捕获和处理
- **业务错误**: 在服务层统一处理

## 配置和注册

### 自动注册
```java
@Configuration
public class PaymentTradeConfiguration {
    
    @PostConstruct
    public void registerChannelComponents() {
        // 自动注册所有适配器
        for (ChannelAdapter adapter : channelAdapters) {
            paymentClientService.registerAdapter(adapter);
        }
        
        // 自动注册所有客户端
        for (ChannelClient client : channelClients) {
            paymentClientService.registerClient(client);
        }
    }
}
```

### 组件验证
系统会自动验证适配器和客户端的匹配性，确保每个适配器都有对应的客户端实现。

## 使用示例

```java
// 构建支付请求
UnifiedPaymentRequest request = UnifiedPaymentRequest.builder()
    .paymentNo("ORDER_123")
    .description("测试订单")
    .totalAmount(9999)
    .channelConfig(channelConfig)
    .payer(payer)
    .build();

// 发起支付（内部会自动使用适配器和客户端）
UnifiedPaymentResult result = paymentClientService.pay(request);
```

## 扩展指南

### 新增支付渠道

1. **实现适配器**:
```java
@Component
public class NewChannelAdapter implements ChannelAdapter {
    // 实现参数转换逻辑
}
```

2. **实现客户端**:
```java
@Component
public class NewChannelClient implements ChannelClient {
    // 实现API调用逻辑
}
```

3. **自动注册**: Spring会自动发现并注册新的组件

### 扩展现有功能

- **修改参数转换**: 只需修改对应的适配器
- **修改API调用**: 只需修改对应的客户端
- **添加新功能**: 在接口中添加默认方法，然后在需要的实现中覆盖

## 总结

新的架构设计通过职责分离，实现了：
- **更清晰的代码结构**
- **更好的可测试性**
- **更强的可维护性**
- **更高的可扩展性**

这为后续的功能扩展和渠道接入提供了坚实的基础。
