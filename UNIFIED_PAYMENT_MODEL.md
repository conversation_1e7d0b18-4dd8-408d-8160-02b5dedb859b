# 统一支付模型设计文档

## 概述

本文档描述了支付SDK的统一支付模型设计，采用**统一模型 + 适配器模式**的架构，为不同的支付渠道（智付、微信、支付宝等）提供标准化的接口。

## 核心设计理念

### 1. 统一模型
- 所有接口的入参和出参都使用标准化的数据传输对象（DTO）
- 统一的状态枚举、错误码和异常体系
- 渠道无关的配置抽象

### 2. 适配器模式
- 每个支付渠道都有专属的适配器实现
- 适配器负责统一模型与渠道特定协议之间的双向转换
- 支持特性发现机制，优雅处理渠道功能差异

## 核心组件

### 1. 统一请求模型 (UnifiedPaymentRequest)

```java
public class UnifiedPaymentRequest {
    private String paymentNo;         // 业务方唯一订单号
    private BigDecimal amount;        // 交易金额（元）
    private String subject;           // 订单标题
    private String currency;          // 币种（默认CNY）
    private Long tenantId;            // 租户ID（SaaS场景）
    private String businessLine;      // 业务线标识
    private String paymentMethod;     // 支付方式
    private String platform;         // 应用平台
    private ChannelConfig channelConfig; // 渠道配置
    // ... 其他字段
}
```

### 2. 统一响应模型 (UnifiedPaymentResult)

```java
public class UnifiedPaymentResult {
    private PaymentStatus status;           // 统一状态
    private String paymentOrderId;          // SDK内部支付单ID
    private String channelTransactionId;    // 渠道交易流水号
    private PaymentCredential credential;   // 支付凭证（二维码等）
    private String rawResponse;             // 渠道原始报文
    private UnifiedError error;             // 错误信息
    // ... 其他字段
}
```

### 3. 通用渠道配置 (ChannelConfig)

```java
public class ChannelConfig {
    private String channelCode;       // 渠道标识符
    private String merchantNo;        // 商户号
    private String appId;             // 应用ID
    private String secretKey;         // 签名密钥
    private String publicKey;         // 公钥
    private String privateKey;        // 私钥
    private Map<String, Object> extraConfig; // 扩展配置
    // ... 其他字段
}
```

### 4. 统一状态枚举 (PaymentStatus)

- `SUCCESS`: 支付成功
- `FAILED`: 支付失败
- `PENDING`: 支付处理中
- `CLOSED`: 支付已关闭
- `CANCELLED`: 支付已取消
- `REFUNDED`: 支付已退款
- `PARTIAL_REFUNDED`: 部分退款

### 5. 特性发现机制 (FeatureType)

```java
public enum FeatureType {
    REAL_TIME_PROFIT_SHARING,    // 实时分账
    DELAY_PROFIT_SHARING,        // 延迟分账
    ALLOW_ONE_CENT_ORDER,        // 一分钱订单
    REFUND_SUPPORT,              // 支持退款
    H5_PAYMENT_SUPPORT,          // 支持H5支付
    MINI_APP_PAYMENT_SUPPORT,    // 支持小程序支付
    // ... 其他特性
}
```

## 服务接口

### 1. 支付客户端服务 (PaymentClientService)

```java
public interface PaymentClientService {
    UnifiedPaymentResult pay(UnifiedPaymentRequest request);
    // TODO: 后续扩展
    // UnifiedRefundResult refund(UnifiedRefundRequest request);
    // UnifiedQueryResult queryPayment(UnifiedQueryRequest request);
}
```

### 2. 渠道适配器接口 (ChannelAdapter)

```java
public interface ChannelAdapter {
    String getChannelCode();
    String getChannelName();
    Set<FeatureType> getSupportedFeatures();
    UnifiedPaymentResult doPay(UnifiedPaymentRequest request);
    // TODO: 后续扩展其他方法
}
```

## 异常体系

### 1. 基础异常 (PaymentException)
- 包含错误码和详细信息
- 支持异常链

### 2. 渠道异常 (ChannelException)
- 继承自PaymentException
- 包含渠道名称和渠道原始错误信息

### 3. 签名异常 (SignatureException)
- 专门处理签名验证相关错误

## 使用示例

### 智付微信小程序支付

```java
// 1. 构建渠道配置
ChannelConfig channelConfig = ChannelConfig.builder()
    .channelCode("zhifu")
    .merchantNo("your_merchant_no")
    .privateKey("your_private_key")
    .publicKey("your_public_key")
    .build();

// 2. 构建支付请求
UnifiedPaymentRequest request = UnifiedPaymentRequest.builder()
    .paymentNo("ORDER_" + System.currentTimeMillis())
    .amount(new BigDecimal("99.99"))
    .subject("测试商品")
    .tenantId(1001L)
    .businessLine("saas")
    .paymentMethod("wechat")
    .platform("miniapp")
    .channelConfig(channelConfig)
    .build();

// 3. 发起支付
UnifiedPaymentResult result = paymentClientService.pay(request);

// 4. 处理结果
if (result.isSuccess()) {
    System.out.println("支付成功");
} else if (result.isPending()) {
    System.out.println("二维码: " + result.getCredential().getContent());
} else {
    System.out.println("支付失败: " + result.getError().getMessage());
}
```

## 架构优势

1. **统一接口**: 业务方只需要学习一套API，无需关心底层渠道差异
2. **易于扩展**: 新增支付渠道只需实现ChannelAdapter接口
3. **特性发现**: 自动检查渠道功能支持，避免无效调用
4. **错误处理**: 统一的异常体系，便于错误处理和监控
5. **可测试性**: 清晰的接口边界，便于单元测试
6. **多租户支持**: 原生支持SaaS场景的多租户需求

## 后续扩展计划

1. 实现退款、查询、关单等其他接口
2. 添加更多渠道适配器（微信原生、支付宝等）
3. 实现回调处理服务 (CallbackService)
4. 添加分账功能支持
5. 完善监控和日志体系

## 注意事项

1. 当前设计保持了与现有鲜沐商城业务的兼容性
2. SaaS租户通过方法参数传递租户ID
3. 分账功能暂时预留，后续实现
4. 所有金额单位统一为元（BigDecimal类型）
5. 支持链路追踪和日志记录
